// 根据当前标签页是否存在书签，设置 action icon
export const setActionIcon = async () => {
  const [tab] = await browser.tabs.query({
    active: true,
    currentWindow: true,
  });

  if (!tab) return;

  const url = tab.url || tab.pendingUrl;
};

export async function setupActionIcon() {  
  // watching the active tab change
  browser.tabs.onActivated.addListener((activeInfo) => {
    setActionIcon();
  });

  // watching the window focus change
  browser.windows.onFocusChanged.addListener((windowId) => {
    setActionIcon();
  });

  // watching tab (url) update
  browser.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    if (!changeInfo.url) return;
    setActionIcon();
  });
}