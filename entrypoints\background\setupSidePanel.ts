export async function setupSidePanel() {
  // 允许用户在点击操作工具栏图标时打开侧边栏
  // 当在 manifest 中同时配置了 action.default_popup 和 side_panel 时：
  // 如果设置了 chrome.sidePanel.setPanelBehavior({ openPanelOnActionClick: true })
  // 点击工具栏图标会打开 side panel, default_popup 会被忽略
  // 如果没有设置 openPanelOnActionClick: true
  // 点击工具栏图标会打开 popup, side panel 需要通过其他方式打开（如右键菜单、API 调用）
  // try {
  //   await chrome.sidePanel.setPanelBehavior({ openPanelOnActionClick: true });
  // } catch (error) {
  //   console.error('Failed to set side panel behavior:', error);
  // }
}
