name: "<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON> with all AI models (<PERSON>, <PERSON>, DeepSeek…) & AI Agents"
short_name: "Gochat"
description: "<PERSON><PERSON> with all AI models (Gemini, <PERSON>, DeepSeek…) & AI Agents"

page_menu:
  title: "Gochat"
  create_bookmark: "Bookmark this tab"
  open_manager: "Open Side Panel"

form:
  title:
    label: "Title"
    placeholder: "Enter title..."
  url:
    label: "URL"
    placeholder: "Enter URL..."
  note:
    placeholder: "Enter note..."
  emoji:
    placeholder: "Click or paste emoji..."
  tags:
    placeholder: "Tag: Type and press Enter"
    noResults: "No tags found"
    edit: "Edit Tag"
  folder:
    placeholder: "Folder name"
  result:
    saved: "Saved"
  errors: "Form Errors"

error:
  unknown: "An unknown error occurred"

dialog:
  action:
    done: "Done"
    save: "Save"
    delete: "Delete"
    cancel: "Cancel"
    confirm: "Confirm"
  edit_bookmark_title: "Edit Bookmark"
  edit_folder_title: "Edit Folder"
  confirm_delete: "Confirm Delete"
  delete_confirmation: "This action will permanently delete this folder and all its subfolders and bookmarks. This operation cannot be undone!"
  delete_success: "Deleted successfully"
  delete_failed: "Delete failed"
  tag_deleted: "Tag has been deleted"
  batch_edit_title: "Edit Multiple Bookmarks"
  batch_add_tag_title: "Add Tags to Multiple Bookmarks"
  batch_emoji_title: "Set Emoji for Multiple Bookmarks"
  batch_note_title: "Add Notes to Multiple Bookmarks"
  batch_remove_tag_title: "Remove Tags from Bookmarks"
  no_exist_tags: "No exist tags"

context_menu:
  edit: "Edit"
  rename: "Rename"
  delete: "Delete"
  deleteWithCount: "Delete ($1 items)"
  bulkActions: "Bulk Actions"
  bulkAddTag: "Bulk Add Tag"
  bulkRemoveTag: "Bulk Remove Tag"
  bulkNote: "Bulk Note"
  bulkEmoji: "Bulk Emoji"
  openAll: "Open all ($1 items)"
  openInNewWindow: "Open in new window ($1 items)"
  openIncognito: "Open incognito ($1 items)"
  newSubfolder: "New subfolder"

share:
  title: "Share Folder"
  description: "Create a public link to share your bookmark folder"
  selectFolder: "Select Folder"
  selectFolderPlaceholder: "Please select a folder to share"
  publicToggle: "Public Share"
  publicDescription: "When enabled, anyone with the link can access this folder"
  privateDescription: "Only you can access this folder"
  createShare: "Create Share"
  shareCreated: "Share link created"
  copyLink: "Copy Link"
  linkCopied: "Link copied to clipboard"

popup:
  edit:
    add: "Add"
    update: "Update"
    remove: "Remove"
    saved: "Saved!"
    created: "Created!"
    removed: "Removed!"
    newFolder: "New Folder"
  searchFolders: "Search folders..."
  openManager: "All Bookmarks"
  view:
    id: "ID"
    title: "Title"
    url: "URL"
    tags: "Tags"
    note: "Note"
    emoji: "Emoji"

options:
  header:
    logout: "Log out"
    signIn: "Sign in"
  list:
    searchResults: "Search results: $1 items, currently showing $2 items, max 100 items"
    noResults: "No results"
    noResultsDescription: "Try searching or filtering for a different term"
  sort:
    default: "Default"
    id: "ID"
    url: "URL"
    title: "Title"
    dateAdded: "Date Added"
    dateLastUsed: "Date Last Used"
  view: 
    view: "View"
    toggle_columns: "Toggle columns"
    id: "ID"
    url: "URL"
    tags: "Tags"
    note: "Note"
    emoji: "Emoji"
  card:
    emoji: "emoji"
    note: "note"
    tags: "tags"
    date_added: "added"
    last_used: "last used"