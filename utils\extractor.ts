class IntelligentContentExtractor {
  private contentSelectors = [
    // 语义化标签
    'main', 'article', '[role="main"]',
    // 常见内容区域
    '.content', '#content', '.post-content', '.entry-content',
    '.article-content', '.story-content', '.news-content',
    // 中文网站常见
    '.article', '.post', '.detail', '.main-content'
  ];

  private noiseSelectors = [
    // 导航和菜单
    'nav', 'header', 'footer', 'aside',
    '.navigation', '.menu', '.sidebar',
    // 广告和推广
    '.ad', '.ads', '.advertisement', '.promo',
    // 评论和社交
    '.comments', '.social', '.share',
    // 脚本和样式
    'script', 'style', 'noscript'
  ];

  extractMainContent(): ExtractedContent {
    // 1. 尝试使用 Mozilla Readability
    const readabilityResult = this.tryReadability();
    if (readabilityResult && readabilityResult.length > 200) {
      return readabilityResult;
    }

    // 2. 使用语义化选择器
    const semanticContent = this.extractBySelectors();
    if (semanticContent && semanticContent.length > 100) {
      return semanticContent;
    }

    // 3. 使用启发式算法
    return this.extractByHeuristics();
  }

  private tryReadability(): ExtractedContent | null {
    try {
      const documentClone = document.cloneNode(true) as Document;
      const reader = new Readability(documentClone);
      const article = reader.parse();
      
      if (article) {
        return {
          title: article.title,
          content: article.textContent,
          html: article.content,
          excerpt: article.excerpt,
          confidence: 0.9
        };
      }
    } catch (error) {
      console.warn('Readability failed:', error);
    }
    return null;
  }

  private extractBySelectors(): ExtractedContent | null {
    for (const selector of this.contentSelectors) {
      const element = document.querySelector(selector);
      if (element) {
        const content = this.cleanElement(element);
        if (content.length > 100) {
          return {
            title: document.title,
            content: content,
            html: element.innerHTML,
            confidence: 0.7
          };
        }
      }
    }
    return null;
  }

  private extractByHeuristics(): ExtractedContent {
    // 基于文本密度的启发式算法
    const allElements = document.querySelectorAll('p, div, span');
    const candidates: Array<{element: Element, score: number}> = [];

    allElements.forEach(element => {
      const score = this.calculateContentScore(element);
      if (score > 0) {
        candidates.push({ element, score });
      }
    });

    // 按分数排序，选择最佳候选
    candidates.sort((a, b) => b.score - a.score);
    
    const bestCandidate = candidates[0];
    if (bestCandidate) {
      return {
        title: document.title,
        content: this.cleanElement(bestCandidate.element),
        html: bestCandidate.element.innerHTML,
        confidence: 0.5
      };
    }

    // 兜底方案：提取 body 文本
    return {
      title: document.title,
      content: document.body.innerText,
      html: document.body.innerHTML,
      confidence: 0.2
    };
  }

  private calculateContentScore(element: Element): number {
    let score = 0;
    const text = element.textContent || '';
    
    // 文本长度分数
    score += Math.min(text.length / 100, 10);
    
    // 段落密度分数
    const paragraphs = element.querySelectorAll('p').length;
    score += paragraphs * 2;
    
    // 链接密度惩罚
    const links = element.querySelectorAll('a').length;
    const linkDensity = links / Math.max(text.length / 100, 1);
    score -= linkDensity * 3;
    
    // 类名和ID加分
    const className = element.className.toLowerCase();
    const id = element.id.toLowerCase();
    
    if (className.includes('content') || className.includes('article')) {
      score += 5;
    }
    if (className.includes('sidebar') || className.includes('nav')) {
      score -= 5;
    }
    
    return score;
  }

  private cleanElement(element: Element): string {
    const clone = element.cloneNode(true) as Element;
    
    // 移除噪音元素
    this.noiseSelectors.forEach(selector => {
      clone.querySelectorAll(selector).forEach(el => el.remove());
    });
    
    // 清理文本
    return clone.textContent?.replace(/\s+/g, ' ').trim() || '';
  }
}