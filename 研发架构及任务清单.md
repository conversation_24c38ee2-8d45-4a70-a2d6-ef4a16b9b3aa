# GoChat Chrome 扩展研发架构及任务清单

## 项目概述

基于 Vue 3 + WXT + TailwindCSS v4 + Shadcn 的智能对话 Chrome 扩展，支持多模型切换、网页内容上下文感知、标签页级隔离的对话体验。

## 核心业务流程

### 1. 用户首次使用流程
```mermaid
flowchart TD
    A[用户安装扩展] --> B[打开SidePanel]
    B --> C[生成设备指纹]
    C --> D[创建匿名用户]
    D --> E[显示欢迎引导]
    E --> F[初始化配额: 10K tokens]
    F --> G[展示可用模型列表]
    G --> H[开始对话]

    H --> I{配额检查}
    I -->|充足| J[发送消息到AI]
    I -->|不足| K[显示升级提醒]

    J --> L[扣除配额]
    L --> M[显示AI回复]

    K --> N[引导用户注册]
    N --> O[数据迁移]
    O --> P[升级为注册用户]
```

### 2. 核心对话流程（最重要）
```mermaid
flowchart TD
    A[用户在SidePanel输入消息] --> B[Content Script提取页面内容]
    B --> C[解析页面结构]
    C --> D[提取关键信息]

    D --> E[页面标题]
    D --> F[主要文本内容]
    D --> G[选中文本]
    D --> H[页面URL和元数据]

    E --> I[构建上下文对象]
    F --> I
    G --> I
    H --> I

    I --> J[应用上下文压缩算法]
    J --> K[套用系统Prompt模板]

    K --> L[组装完整消息]
    L --> M{检查配额}
    M -->|不足| N[显示配额限制]
    M -->|充足| O[发送到AI代理服务]

    O --> P[后端验证用户权限]
    P --> Q[选择对应AI Provider]
    Q --> R[发送到AI模型]
    R --> S[接收流式响应]

    S --> T[实时显示回复]
    T --> U[计算Token消耗]
    U --> V[更新配额使用量]
    V --> W[保存对话记录]
    W --> X[更新会话状态]

    Y[用户继续对话] --> Z[保持上下文连续性]
    Z --> AA[合并历史消息]
    AA --> L
```

### 3. 标签页上下文感知流程
```mermaid
flowchart TD
    A[用户切换标签页] --> B[检测URL变化]
    B --> C[提取页面内容]
    C --> D{是否有该标签页的会话}
    D -->|有| E[恢复历史会话]
    D -->|无| F[创建新会话]

    E --> G[加载上下文]
    F --> G
    G --> H[用户发起对话]
    H --> I[组合消息: 用户输入 + 页面上下文]
    I --> J[发送到AI模型]
    J --> K[保存到会话历史]
    K --> L[显示回复]

    M[用户选择文本] --> N[右键菜单]
    N --> O[快速对话选项]
    O --> P[将选中文本作为重点上下文]
    P --> I
```

### 4. 多模型切换流程
```mermaid
flowchart TD
    A[用户点击模型选择器] --> B{检查用户类型}
    B -->|匿名用户| C[显示受限模型列表]
    B -->|注册用户| D[显示扩展模型列表]
    B -->|付费用户| E[显示全部模型列表]

    C --> F[GPT-3.5, Claude-3-Haiku]
    D --> G[+ GPT-4o-mini, Claude-3-Sonnet]
    E --> H[+ GPT-4, Claude-3-Opus, Gemini-Pro]

    F --> I{用户选择模型}
    G --> I
    H --> I

    I -->|可用模型| J[切换成功]
    I -->|受限模型| K[显示升级提示]

    J --> L[更新会话配置]
    L --> M[继续对话]

    K --> N[引导注册/升级]
```

### 5. 配额管理流程
```mermaid
flowchart TD
    A[用户发送消息] --> B[预估Token消耗]
    B --> C{检查当日配额}
    C -->|充足| D[发送请求]
    C -->|不足| E[配额超限提醒]

    D --> F[AI服务响应]
    F --> G[计算实际Token消耗]
    G --> H[更新配额使用量]
    H --> I[保存使用记录]

    E --> J{用户选择}
    J -->|注册升级| K[账户升级]
    J -->|等待重置| L[显示重置时间]
    J -->|购买配额| M[支付流程]

    K --> N[配额立即提升]
    N --> D

    O[每日0点] --> P[重置所有用户配额]
    P --> Q[发送配额重置通知]
```

### 6. 用户升级和数据迁移流程
```mermaid
flowchart TD
    A[匿名用户点击注册] --> B[OAuth认证]
    B --> C[获取用户信息]
    C --> D[创建注册用户记录]
    D --> E[开始数据迁移]

    E --> F[迁移对话历史]
    F --> G[迁移配额记录]
    G --> H[迁移用户偏好]
    H --> I[更新会话归属]
    I --> J[升级配额限制]
    J --> K[清理匿名数据]

    K --> L[迁移完成通知]
    L --> M[展示升级后功能]
    M --> N[继续使用]

    O[迁移失败] --> P[回滚操作]
    P --> Q[保持匿名状态]
    Q --> R[错误提示]
```

## 技术架构

### 核心技术栈
- **前端框架**: Vue 3.5 + Composition API + TypeScript
- **扩展框架**: WXT 0.20.7 (基于 Vite)
- **UI 框架**: TailwindCSS v4 + Reka UI (Shadcn Vue 版本)
- **状态管理**: Pinia + VueUse
- **数据存储**: RxDB (本地) + Chrome Storage API
- **路由**: Vue Router 4.5
- **国际化**: @wxt-dev/i18n
- **图标**: Lucide Vue Next + Iconify

### 扩展架构设计

#### 1. 入口点架构 (Entrypoints)
```
entrypoints/
├── background/           # 后台服务
│   ├── index.ts         # 主入口
│   ├── ai-service.ts    # AI 模型服务管理
│   ├── context-manager.ts # 网页内容管理
│   ├── auth-manager.ts  # 认证管理
│   └── billing-manager.ts # 计费管理
├── content/             # 内容脚本
│   ├── index.ts         # 主入口
│   ├── page-analyzer.ts # 页面内容分析
│   ├── selection-handler.ts # 文本选择处理
│   └── media-extractor.ts # 媒体资源提取
├── side-panel/          # 侧边栏主界面
│   ├── App.vue          # 主应用
│   ├── components/      # 组件
│   └── pages/           # 页面
├── popup/               # 弹窗界面
└── options/             # 设置页面
```

#### 2. 数据层架构

##### 本地存储 (RxDB)
```typescript
// 数据库 Schema 设计
interface ChatSession {
  id: string;
  tabId: number;
  title: string;
  model: string;
  context: PageContext;
  messages: Message[];
  userId?: string;          // 注册用户ID，匿名用户为空
  deviceId: string;         // 设备指纹ID
  userType: UserType;       // 用户类型
  createdAt: string;
  updatedAt: string;
}

interface Message {
  id: string;
  sessionId: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
  tokens?: number;
  cost?: number;            // 消耗的配额
}

interface PageContext {
  url: string;
  title: string;
  content: string;
  selectedText?: string;
  mediaResources?: MediaResource[];
}

interface UserPreferences {
  defaultModel: string;
  apiKeys: Record<string, string>;
  prompts: SavedPrompt[];
  theme: 'light' | 'dark';
  userType: UserType;
}

interface QuotaUsage {
  id: string;
  userId?: string;          // 注册用户ID
  deviceId: string;         // 匿名用户设备ID
  date: string;             // YYYY-MM-DD
  tokensUsed: number;
  tokensLimit: number;
  userType: UserType;
  resetAt: string;
  createdAt: string;
  updatedAt: string;
}
```

##### Chrome Storage 同步
- 用户设置和 API 密钥
- 预置 Prompt 模板
- 主题和界面配置

#### 3. AI 服务架构

##### 多模型支持
```typescript
interface AIProvider {
  name: string;
  models: AIModel[];
  apiEndpoint: string;
  authenticate(apiKey: string): Promise<boolean>;
  chat(messages: Message[], options: ChatOptions): Promise<Response>;
}

// 支持的 AI 提供商
const providers = [
  'OpenAI',      // GPT-4, GPT-3.5
  'Anthropic',   // Claude
  'Google',      // Gemini
  'Cohere',      // Command
  'Mistral',     // Mistral 7B/8x7B
  'Ollama',      // 本地模型
  'Azure OpenAI',
  'AWS Bedrock'
];
```

##### 流式响应处理
- Server-Sent Events (SSE) 支持
- 实时消息流显示
- 错误处理和重试机制

#### 4. 上下文管理架构

##### 页面内容提取
```typescript
interface ContentExtractor {
  extractText(): string;
  extractStructure(): PageStructure;
  extractMedia(): MediaResource[];
  extractSelection(): string;
  watchChanges(): Observable<ContentChange>;
}
```

##### 标签页隔离
- 每个标签页独立的对话会话
- 基于 tabId 的上下文隔离
- 标签页切换时的会话恢复

#### 5. 用户权限和配额架构

##### 用户类型定义
```typescript
enum UserType {
  ANONYMOUS = 'anonymous',    // 未登录用户
  REGISTERED = 'registered',  // 已注册用户
  PREMIUM = 'premium'         // 付费用户
}

interface UserQuota {
  userType: UserType;
  dailyTokenLimit: number;
  availableModels: string[];
  features: string[];
  resetTime: string;
}

// 配额配置
const QUOTA_CONFIG = {
  [UserType.ANONYMOUS]: {
    dailyTokenLimit: 10000,     // 每日1万token
    availableModels: ['gpt-3.5-turbo', 'claude-3-haiku'],
    features: ['basic_chat'],
    resetTime: '00:00:00'       // 每日重置
  },
  [UserType.REGISTERED]: {
    dailyTokenLimit: 50000,     // 每日5万token
    availableModels: ['gpt-3.5-turbo', 'gpt-4o-mini', 'claude-3-haiku', 'claude-3-sonnet'],
    features: ['basic_chat', 'context_analysis', 'prompt_save'],
    resetTime: '00:00:00'
  },
  [UserType.PREMIUM]: {
    dailyTokenLimit: 500000,    // 每日50万token
    availableModels: ['*'],     // 所有模型
    features: ['*'],            // 所有功能
    resetTime: '00:00:00'
  }
};
```

##### 匿名用户识别策略
```typescript
interface AnonymousUser {
  deviceId: string;           // 基于浏览器指纹生成
  installId: string;          // 扩展安装时生成的唯一ID
  sessionId: string;          // 当前会话ID
  createdAt: string;
  lastActiveAt: string;
}

// 设备指纹生成
class DeviceFingerprint {
  static async generate(): Promise<string> {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    ctx.textBaseline = 'top';
    ctx.font = '14px Arial';
    ctx.fillText('Device fingerprint', 2, 2);

    const fingerprint = {
      canvas: canvas.toDataURL(),
      userAgent: navigator.userAgent,
      language: navigator.language,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      screen: `${screen.width}x${screen.height}`,
      colorDepth: screen.colorDepth,
      platform: navigator.platform
    };

    return btoa(JSON.stringify(fingerprint)).slice(0, 32);
  }
}
```

##### 权限管理
```json
{
  "permissions": [
    "sidePanel",
    "tabs",
    "activeTab",
    "storage",
    "contextMenus",
    "identity",
    "scripting"
  ],
  "host_permissions": [
    "https://*/*",
    "http://*/*"
  ]
}
```

##### 安全措施
- 设备指纹 + 安装ID 双重匿名用户识别
- API 密钥服务端代理，客户端不存储
- 内容脚本沙箱隔离
- CSP 策略配置
- 配额数据加密存储

## 核心业务实现架构

### 1. 匿名用户支持架构

#### 1.1 匿名用户生命周期管理
```typescript
class AnonymousUserLifecycle {
  // 用户首次使用时初始化
  async initializeAnonymousUser(): Promise<AnonymousUser> {
    const deviceId = await DeviceFingerprint.generate();
    const installId = await this.getOrCreateInstallId();

    const anonymousUser: AnonymousUser = {
      deviceId,
      installId,
      sessionId: cuid(),
      createdAt: new Date().toISOString(),
      lastActiveAt: new Date().toISOString()
    };

    // 初始化配额
    await this.initializeQuota(deviceId);
    return anonymousUser;
  }

  // 配额检查和消费
  async consumeTokens(deviceId: string, tokens: number): Promise<boolean> {
    const quota = await this.getQuota(deviceId);
    if (quota.tokensUsed + tokens > quota.tokensLimit) {
      throw new QuotaExceededException('Daily quota exceeded');
    }

    quota.tokensUsed += tokens;
    await this.saveQuota(deviceId, quota);
    return true;
  }

  // 升级为注册用户
  async upgradeToRegistered(deviceId: string, userId: string): Promise<void> {
    // 迁移匿名用户数据到注册用户
    const sessions = await this.getAnonymousSessions(deviceId);
    const quota = await this.getQuota(deviceId);

    // 更新会话归属
    for (const session of sessions) {
      session.userId = userId;
      session.userType = UserType.REGISTERED;
      await this.updateSession(session);
    }

    // 迁移配额数据
    quota.userId = userId;
    quota.userType = UserType.REGISTERED;
    quota.tokensLimit = QUOTA_CONFIG[UserType.REGISTERED].dailyTokenLimit;
    await this.saveQuota(deviceId, quota);
  }
}
```

#### 1.2 配额限制实现
```typescript
class QuotaEnforcer {
  async enforceModelAccess(userType: UserType, requestedModel: string): Promise<boolean> {
    const config = QUOTA_CONFIG[userType];

    // 检查模型访问权限
    if (config.availableModels.includes('*') ||
        config.availableModels.includes(requestedModel)) {
      return true;
    }

    throw new ModelAccessDeniedException(
      `Model ${requestedModel} not available for ${userType} users`
    );
  }

  async enforceFeatureAccess(userType: UserType, feature: string): Promise<boolean> {
    const config = QUOTA_CONFIG[userType];

    if (config.features.includes('*') || config.features.includes(feature)) {
      return true;
    }

    throw new FeatureAccessDeniedException(
      `Feature ${feature} not available for ${userType} users`
    );
  }

  async enforceTokenLimit(deviceId: string, requestedTokens: number): Promise<void> {
    const usage = await this.getUsageToday(deviceId);
    const userType = await this.getUserType(deviceId);
    const limit = QUOTA_CONFIG[userType].dailyTokenLimit;

    if (usage.tokensUsed + requestedTokens > limit) {
      throw new QuotaExceededException({
        current: usage.tokensUsed,
        requested: requestedTokens,
        limit: limit,
        resetTime: usage.resetAt
      });
    }
  }
}
```

#### 1.3 服务端代理架构
```typescript
// 后端 API 代理服务
class AIProxyService {
  async proxyRequest(request: ChatRequest): Promise<ChatResponse> {
    // 验证用户身份和配额
    const user = await this.authenticateUser(request.deviceId, request.userId);
    await this.enforceQuota(user, request.estimatedTokens);

    // 代理请求到 AI 服务
    const response = await this.forwardToAIProvider(request);

    // 记录实际使用量
    await this.recordUsage(user, response.actualTokens);

    return response;
  }

  private async authenticateUser(deviceId: string, userId?: string): Promise<UserContext> {
    if (userId) {
      // 注册用户验证
      return await this.validateRegisteredUser(userId);
    } else {
      // 匿名用户验证
      return await this.validateAnonymousUser(deviceId);
    }
  }
}
```

### 2. 核心对话系统架构

#### 2.1 页面内容提取器

##### 推荐开源库方案
1. **Mozilla Readability** (主要方案)
   - Firefox Reader Mode 的核心算法
   - 专门用于提取文章主要内容，支持中文
   - 能识别正文、标题、作者等结构化信息
   - 准确率高，适合新闻、博客等内容型网站

2. **Boilerpipe** (备选方案)
   - 专门用于去除网页"样板"内容
   - 算法成熟，在学术界广泛使用
   - 适合处理复杂布局的网站

3. **自定义智能提取器** (兜底方案)
   - 多策略组合：语义化选择器 + 启发式算法
   - 针对不同网站类型的专门策略
   - 内容质量评估和置信度计算

##### 提取策略设计
```typescript
interface ContentExtractor {
  // 提取页面主要文本内容
  extractMainContent(): ExtractedContent;

  // 提取页面结构化信息
  extractPageStructure(): PageStructure;

  // 获取用户选中的文本
  getSelectedText(): string;

  // 提取媒体资源信息
  extractMediaResources(): MediaResource[];

  // 监听页面变化
  watchPageChanges(): Observable<ContentChange>;
}

interface ExtractedContent {
  title: string;
  content: string;        // 纯文本内容
  html?: string;          // HTML 内容
  excerpt?: string;       // 摘要
  confidence: number;     // 提取置信度 0-1
  contentType: 'article' | 'product' | 'list' | 'other';
  quality: ContentQuality;
}

interface ContentQuality {
  textLength: number;
  paragraphCount: number;
  linkDensity: number;
  hasStructure: boolean;  // 是否有良好的段落结构
}

// 多策略内容提取实现
class IntelligentContentExtractor implements ContentExtractor {
  extractMainContent(): ExtractedContent {
    // 1. 尝试 Mozilla Readability (优先级最高)
    const readabilityResult = this.tryReadability();
    if (readabilityResult?.confidence > 0.8) {
      return readabilityResult;
    }

    // 2. 使用语义化选择器策略
    const semanticResult = this.extractBySelectors();
    if (semanticResult?.confidence > 0.6) {
      return semanticResult;
    }

    // 3. 启发式算法兜底
    return this.extractByHeuristics();
  }

  private contentSelectors = {
    // 通用语义化标签
    semantic: ['main', 'article', '[role="main"]'],

    // 新闻网站
    news: ['.article-body', '.story-body', '.news-content'],

    // 博客网站
    blog: ['.entry-content', '.post-content', '.article-content'],

    // 电商网站
    ecommerce: ['.product-description', '.product-details'],

    // 中文网站常见
    chinese: ['.article', '.post', '.detail', '.main-content']
  };

  private noiseSelectors = [
    // 导航和结构
    'nav', 'header', 'footer', 'aside', '.navigation', '.menu', '.sidebar',

    // 广告和推广
    '.ad', '.ads', '.advertisement', '.promo', '.sponsored',

    // 社交和互动
    '.comments', '.social', '.share', '.related', '.recommendations',

    // 脚本和样式
    'script', 'style', 'noscript', '.hidden'
  ];
}
```

##### 内容质量评估算法
```typescript
interface QualityMetrics {
  confidence: number;      // 整体置信度
  relevance: number;       // 内容相关性
  completeness: number;    // 内容完整性
  readability: number;     // 可读性
}

function assessContentQuality(content: ExtractedContent): QualityMetrics {
  const textLength = content.content.length;
  const paragraphCount = content.quality.paragraphCount;
  const linkDensity = content.quality.linkDensity;

  // 基于长度的评分
  const lengthScore = Math.min(textLength / 1000, 1);

  // 基于结构的评分
  const structureScore = paragraphCount > 3 ? 0.8 : 0.4;

  // 链接密度惩罚
  const linkPenalty = Math.max(0, linkDensity - 0.1) * 2;

  const confidence = Math.max(0, Math.min(1,
    (lengthScore * 0.4 + structureScore * 0.4 + 0.2) - linkPenalty
  ));

  return {
    confidence,
    relevance: content.confidence,
    completeness: lengthScore,
    readability: structureScore
  };
}
```

#### 2.2 上下文压缩算法
```typescript
class ContextCompressor {
  // 智能压缩页面内容，保留关键信息
  compress(content: string, maxTokens: number): string {
    // 1. 提取关键段落
    const keyParagraphs = this.extractKeyParagraphs(content);

    // 2. 保留重要信息（标题、链接、数据等）
    const importantInfo = this.extractImportantInfo(content);

    // 3. 按重要性排序并截断
    return this.prioritizeAndTruncate(keyParagraphs, importantInfo, maxTokens);
  }

  // 估算 Token 数量
  estimateTokens(text: string): number {
    // 简单估算：1 token ≈ 4 字符（英文）或 1.5 字符（中文）
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    const otherChars = text.length - chineseChars;
    return Math.ceil(chineseChars / 1.5 + otherChars / 4);
  }
}
```

#### 2.3 系统 Prompt 模板引擎
```typescript
class PromptTemplateEngine {
  private systemPrompts = {
    default: `你是一个智能网页助手，能够理解和分析网页内容，为用户提供有价值的见解和帮助。

当前网页信息：
- 标题：{{pageTitle}}
- URL：{{pageUrl}}
- 域名：{{domain}}

网页内容摘要：
{{pageContent}}

{{#if selectedText}}
用户选中的文本：
{{selectedText}}
{{/if}}

请基于以上网页内容回答用户的问题。如果问题与网页内容相关，请优先使用网页信息；如果无关，请提供通用的帮助。`,

    analysis: `请分析这个网页的内容，包括：
1. 主要主题和关键信息
2. 内容质量和可信度
3. 对用户可能有价值的要点
4. 相关的建议或行动项`,

    summary: `请为这个网页内容提供一个简洁的摘要，包括：
1. 核心观点（3-5个要点）
2. 关键数据或事实
3. 主要结论或建议`
  };

  render(template: string, context: PromptContext): string {
    return template
      .replace(/\{\{pageTitle\}\}/g, context.pageTitle)
      .replace(/\{\{pageUrl\}\}/g, context.pageUrl)
      .replace(/\{\{domain\}\}/g, context.domain)
      .replace(/\{\{pageContent\}\}/g, context.pageContent)
      .replace(/\{\{selectedText\}\}/g, context.selectedText || '');
  }
}
```

#### 2.4 消息组装和发送
```typescript
class MessageComposer {
  async composeMessage(
    userInput: string,
    pageContext: PageContext,
    chatHistory: Message[]
  ): Promise<ChatMessage> {
    // 1. 压缩页面内容
    const compressedContent = await this.contextCompressor.compress(
      pageContext.content,
      2000 // 最大2000 tokens
    );

    // 2. 构建上下文对象
    const context: PromptContext = {
      pageTitle: pageContext.title,
      pageUrl: pageContext.url,
      domain: pageContext.domain,
      pageContent: compressedContent,
      selectedText: pageContext.selectedText
    };

    // 3. 渲染系统 Prompt
    const systemPrompt = this.promptEngine.render('default', context);

    // 4. 组装完整消息
    const messages: ChatMessage[] = [
      { role: 'system', content: systemPrompt },
      ...chatHistory.slice(-10), // 保留最近10条历史
      { role: 'user', content: userInput }
    ];

    return messages;
  }
}
```

#### 2.5 流式响应处理
```typescript
class StreamingChatService {
  async sendMessage(
    messages: ChatMessage[],
    model: string,
    onChunk: (chunk: string) => void,
    onComplete: (usage: TokenUsage) => void
  ): Promise<void> {
    const response = await fetch('/api/chat/stream', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${await this.getAuthToken()}`
      },
      body: JSON.stringify({
        messages,
        model,
        stream: true,
        deviceId: await this.getDeviceId()
      })
    });

    const reader = response.body?.getReader();
    const decoder = new TextDecoder();

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      const chunk = decoder.decode(value);
      const lines = chunk.split('\n');

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const data = line.slice(6);
          if (data === '[DONE]') {
            return;
          }

          try {
            const parsed = JSON.parse(data);
            if (parsed.choices?.[0]?.delta?.content) {
              onChunk(parsed.choices[0].delta.content);
            }
            if (parsed.usage) {
              onComplete(parsed.usage);
            }
          } catch (e) {
            console.warn('Failed to parse SSE data:', data);
          }
        }
      }
    }
  }
}
```

#### 2.6 对话持续性管理
```typescript
class ConversationManager {
  private sessions: Map<number, ChatSession> = new Map();

  async getOrCreateSession(tabId: number): Promise<ChatSession> {
    if (this.sessions.has(tabId)) {
      return this.sessions.get(tabId)!;
    }

    // 从数据库恢复会话
    const existingSession = await this.db.sessions
      .findOne({ selector: { tabId } })
      .exec();

    if (existingSession) {
      this.sessions.set(tabId, existingSession);
      return existingSession;
    }

    // 创建新会话
    const newSession = await this.createNewSession(tabId);
    this.sessions.set(tabId, newSession);
    return newSession;
  }

  async addMessage(sessionId: string, message: Message): Promise<void> {
    const session = await this.getSessionById(sessionId);
    session.messages.push(message);
    session.updatedAt = new Date().toISOString();

    // 保存到数据库
    await session.save();

    // 限制历史消息数量（保留最近50条）
    if (session.messages.length > 50) {
      session.messages = session.messages.slice(-50);
      await session.save();
    }
  }

  // 智能上下文管理 - 保持对话连贯性
  getContextualMessages(session: ChatSession, maxTokens: number = 4000): Message[] {
    const messages = session.messages;
    let totalTokens = 0;
    const contextMessages: Message[] = [];

    // 从最新消息开始，向前选择消息直到达到token限制
    for (let i = messages.length - 1; i >= 0; i--) {
      const message = messages[i];
      const messageTokens = this.estimateTokens(message.content);

      if (totalTokens + messageTokens > maxTokens) {
        break;
      }

      contextMessages.unshift(message);
      totalTokens += messageTokens;
    }

    return contextMessages;
  }
}
```

### 3. 前端用户体验设计

#### 2.1 匿名用户引导流程
```typescript
// 用户引导组件
interface UserOnboardingFlow {
  // 首次使用引导
  showWelcomeDialog(): void;

  // 配额状态显示
  displayQuotaStatus(usage: QuotaUsage): void;

  // 升级提醒
  showUpgradePrompt(reason: 'quota_exceeded' | 'model_restricted'): void;

  // 功能限制提示
  showFeatureLimitedTooltip(feature: string): void;
}

// 配额状态组件
const QuotaIndicator = {
  template: `
    <div class="quota-indicator" :class="quotaStatusClass">
      <Icon :name="quotaIcon" />
      <span>{{ quotaText }}</span>
      <Button v-if="showUpgrade" @click="showUpgradeDialog">升级</Button>
    </div>
  `,
  computed: {
    quotaStatusClass() {
      if (this.usage.percentage > 90) return 'quota-critical';
      if (this.usage.percentage > 70) return 'quota-warning';
      return 'quota-normal';
    }
  }
};
```

#### 2.2 模型选择器增强
```typescript
// 模型选择组件
const ModelSelector = {
  template: `
    <Select v-model="selectedModel">
      <SelectTrigger>
        <SelectValue placeholder="选择模型" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem
          v-for="model in availableModels"
          :key="model.id"
          :value="model.id"
          :disabled="!model.accessible"
        >
          <div class="flex items-center justify-between w-full">
            <span>{{ model.name }}</span>
            <Badge v-if="model.isPremium" variant="premium">Pro</Badge>
            <Tooltip v-if="!model.accessible">
              <TooltipTrigger>
                <Icon name="lock" class="w-4 h-4" />
              </TooltipTrigger>
              <TooltipContent>
                需要注册账户才能使用此模型
              </TooltipContent>
            </Tooltip>
          </div>
        </SelectItem>
      </SelectContent>
    </Select>
  `
};
```

### 3. 对话系统
```typescript
// 对话管理器
class ChatManager {
  private sessions: Map<number, ChatSession> = new Map();

  async createSession(tabId: number, userType: UserType): Promise<ChatSession>;
  async sendMessage(sessionId: string, content: string): Promise<void>;
  async switchModel(sessionId: string, model: string): Promise<void>;
  async exportSession(sessionId: string): Promise<string>;
  async checkQuotaBeforeSend(sessionId: string, estimatedTokens: number): Promise<boolean>;
}
```

### 2. 上下文感知
```typescript
// 上下文提供器
class ContextProvider {
  async getPageContext(tabId: number): Promise<PageContext>;
  async getSelectedText(tabId: number): Promise<string>;
  async extractMediaInfo(element: HTMLElement): Promise<MediaResource>;
  watchPageChanges(tabId: number): Observable<PageChange>;
}
```

### 3. 模型管理
```typescript
// AI 模型管理器
class ModelManager {
  private providers: Map<string, AIProvider> = new Map();
  
  async registerProvider(provider: AIProvider): Promise<void>;
  async validateApiKey(provider: string, apiKey: string): Promise<boolean>;
  async chat(provider: string, model: string, messages: Message[]): Promise<Response>;
  getAvailableModels(): AIModel[];
}
```

### 4. 配额和计费系统
```typescript
// 配额管理器
class QuotaManager {
  private quotaStorage: Map<string, UserQuota> = new Map();

  async getUserQuota(userId: string): Promise<UserQuota>;
  async checkQuotaAvailable(userId: string, tokens: number): Promise<boolean>;
  async consumeQuota(userId: string, tokens: number): Promise<void>;
  async resetDailyQuota(userId: string): Promise<void>;
  async upgradeUserType(userId: string, newType: UserType): Promise<void>;
}

// 匿名用户管理器
class AnonymousUserManager {
  async getOrCreateAnonymousUser(): Promise<AnonymousUser>;
  async trackAnonymousUsage(deviceId: string, tokens: number): Promise<void>;
  async checkAnonymousQuota(deviceId: string): Promise<QuotaInfo>;
  async promoteToRegistered(deviceId: string, userId: string): Promise<void>;
}

// 计费管理器
class BillingManager {
  async trackUsage(sessionId: string, tokens: number, userType: UserType): Promise<void>;
  async checkQuota(userId: string): Promise<QuotaInfo>;
  async purchaseCredits(amount: number): Promise<PaymentResult>;
  getUsageStats(period: string): Promise<UsageStats>;
  async handleQuotaExceeded(userId: string): Promise<UpgradeOptions>;
}
```

## 关键开源库选择

### UI 组件库
- **Reka UI**: Vue 版本的 Radix UI，提供无样式的可访问组件
- **Lucide Vue Next**: 现代图标库
- **Vue Sonner**: 优雅的通知组件

### 数据管理
- **RxDB**: 响应式本地数据库，支持离线和同步
- **Pinia**: Vue 3 官方状态管理
- **VueUse**: Vue 组合式 API 工具集

### 网络请求
- **Fetch API**: 原生 HTTP 客户端
- **EventSource**: SSE 流式响应处理

### 内容提取库
- **@mozilla/readability**: Firefox Reader Mode 核心算法，专门提取文章主要内容
- **boilerpipe**: 去除网页"样板"内容的成熟算法
- **自定义智能提取器**: 多策略组合方案，支持语义化选择器 + 启发式算法

### 工具库
- **Zod**: TypeScript 优先的数据验证
- **date-fns**: 现代日期处理库
- **cuid**: 唯一 ID 生成器

### 开发工具
- **WXT**: 现代扩展开发框架
- **Vite**: 快速构建工具
- **TypeScript**: 类型安全
- **TailwindCSS**: 原子化 CSS 框架

## 详细任务清单

### 阶段一：基础架构搭建 (1-2 周)

#### 1.1 项目结构优化
- [ ] 重构现有目录结构
- [ ] 配置 TypeScript 严格模式
- [ ] 设置 ESLint + Prettier
- [ ] 配置 Vite 构建优化

#### 1.2 用户系统设计
- [ ] 实现匿名用户识别机制
- [ ] 设计设备指纹生成算法
- [ ] 创建用户类型管理系统
- [ ] 实现配额管理架构

#### 1.3 数据库设计
- [ ] 设计 RxDB Schema (ChatSession, Message, UserPreferences, QuotaUsage)
- [ ] 实现数据库迁移策略
- [ ] 创建数据访问层 (DAL)
- [ ] 实现数据同步机制

#### 1.4 基础组件开发
- [ ] 创建聊天界面组件
- [ ] 实现消息气泡组件
- [ ] 开发增强型模型选择器（支持权限显示）
- [ ] 创建配额状态指示器
- [ ] 实现用户引导组件
- [ ] 开发升级提醒对话框
- [ ] 创建设置面板组件

### 阶段二：核心功能实现 (2-3 周)

#### 2.1 AI 服务集成
- [ ] 实现 AI Provider 抽象层
- [ ] 集成 OpenAI API
- [ ] 集成 Anthropic Claude
- [ ] 集成 Google Gemini
- [ ] 实现流式响应处理
- [ ] 添加错误处理和重试

#### 2.2 核心对话系统
- [ ] 实现页面内容提取器（Content Script）
- [ ] 开发智能内容压缩算法
- [ ] 创建系统 Prompt 模板引擎
- [ ] 实现消息组装和上下文管理
- [ ] 开发流式响应处理机制
- [ ] 创建对话持续性管理器

#### 2.3 上下文管理
- [ ] 实现页面结构化解析
- [ ] 开发文本选择监听
- [ ] 创建媒体资源提取器
- [ ] 实现标签页隔离机制
- [ ] 添加上下文压缩和优化

#### 2.4 会话管理系统
- [ ] 实现标签页级会话隔离
- [ ] 开发会话状态持久化
- [ ] 创建对话历史存储和检索
- [ ] 实现会话导出功能
- [ ] 添加对话搜索功能
- [ ] 实现匿名用户数据迁移机制

### 阶段三：高级功能开发 (2-3 周)

#### 3.1 Prompt 管理
- [ ] 设计 Prompt 模板系统
- [ ] 实现预置 Prompt 库
- [ ] 开发自定义 Prompt 编辑器
- [ ] 创建 Prompt 分享机制
- [ ] 实现 Prompt 变量替换

#### 3.2 用户体验优化
- [ ] 实现主题切换
- [ ] 添加快捷键支持
- [ ] 开发拖拽调整界面
- [ ] 实现消息编辑功能
- [ ] 添加打字机效果

#### 3.3 右键菜单集成
- [ ] 实现图片右键分析
- [ ] 添加视频信息提取
- [ ] 创建文本快速对话
- [ ] 开发链接内容分析

### 阶段四：认证和计费 (1-2 周)

#### 4.1 用户认证系统
- [ ] 集成 OAuth 2.0 认证
- [ ] 实现用户会话管理
- [ ] 开发匿名用户升级机制
- [ ] 实现账户数据迁移
- [ ] 添加多设备支持

#### 4.2 配额和计费系统
- [ ] 实现匿名用户配额管理
- [ ] 开发 Token 使用统计
- [ ] 创建配额超限提醒
- [ ] 实现配额重置机制
- [ ] 集成支付网关
- [ ] 创建使用报告和分析

### 阶段五：测试和优化 (1 周)

#### 5.1 测试覆盖
- [ ] 单元测试 (Vitest)
- [ ] 集成测试
- [ ] E2E 测试 (Playwright)
- [ ] 性能测试

#### 5.2 性能优化
- [ ] 代码分割优化
- [ ] 内存使用优化
- [ ] 网络请求优化
- [ ] 缓存策略实现

#### 5.3 发布准备
- [ ] 构建流程优化
- [ ] 文档编写
- [ ] Chrome Web Store 准备
- [ ] 用户手册制作

## 开发规范

### 代码规范
- 使用 TypeScript 严格模式
- 遵循 Vue 3 Composition API 最佳实践
- 采用函数式编程风格
- 实现完整的错误处理

### 提交规范
- 使用 Conventional Commits
- 每个功能独立分支开发
- 代码审查后合并主分支

### 测试策略
- 单元测试覆盖率 > 80%
- 关键路径集成测试
- 用户体验 E2E 测试

## 匿名用户支持的技术实现要点

### 1. 设备指纹稳定性
- **挑战**: 浏览器更新、隐私模式可能导致指纹变化
- **解决方案**: 多重标识符组合 + 模糊匹配算法
- **备选方案**: Chrome Extension Storage API 存储持久化ID

### 2. 配额防刷机制
- **IP限制**: 同一IP每日最大设备数限制
- **行为分析**: 异常使用模式检测
- **时间窗口**: 滑动窗口配额控制
- **设备验证**: 浏览器环境一致性检查

### 3. 数据迁移策略
```typescript
// 匿名用户升级时的数据迁移
class DataMigrationService {
  async migrateAnonymousData(deviceId: string, userId: string): Promise<void> {
    // 1. 迁移对话历史
    const sessions = await this.getAnonymousSessions(deviceId);
    await this.transferSessionsToUser(sessions, userId);

    // 2. 迁移配额使用记录
    const quotaHistory = await this.getQuotaHistory(deviceId);
    await this.mergeQuotaHistory(quotaHistory, userId);

    // 3. 迁移用户偏好设置
    const preferences = await this.getAnonymousPreferences(deviceId);
    await this.mergeUserPreferences(preferences, userId);

    // 4. 清理匿名数据
    await this.cleanupAnonymousData(deviceId);
  }
}
```

### 4. 前端状态管理
```typescript
// Pinia Store for User State
export const useUserStore = defineStore('user', () => {
  const userType = ref<UserType>(UserType.ANONYMOUS);
  const deviceId = ref<string>('');
  const userId = ref<string | null>(null);
  const quotaUsage = ref<QuotaUsage | null>(null);

  const isAnonymous = computed(() => userType.value === UserType.ANONYMOUS);
  const canUseModel = (modelId: string) => {
    const config = QUOTA_CONFIG[userType.value];
    return config.availableModels.includes('*') ||
           config.availableModels.includes(modelId);
  };

  const remainingQuota = computed(() => {
    if (!quotaUsage.value) return 0;
    return quotaUsage.value.tokensLimit - quotaUsage.value.tokensUsed;
  });

  return {
    userType,
    deviceId,
    userId,
    quotaUsage,
    isAnonymous,
    canUseModel,
    remainingQuota
  };
});
```

## 风险评估

### 技术风险
- Chrome API 变更风险 - 中等
- AI 服务稳定性风险 - 中等
- 设备指纹准确性风险 - 中等
- 配额防刷绕过风险 - 中等
- 性能优化挑战 - 低

### 业务风险
- 用户隐私合规 - 高
- API 成本控制 - 高（匿名用户增加成本控制难度）
- 免费额度滥用 - 中等
- 竞品压力 - 中等

## 后续扩展计划

### 功能扩展
- 支持更多 AI 模型
- 添加语音对话功能
- 实现协作对话功能
- 开发移动端支持

### 平台扩展
- Firefox 扩展版本
- Safari 扩展版本
- Edge 扩展版本
- 独立 Web 应用

---

*本文档将随着开发进度持续更新和完善*
