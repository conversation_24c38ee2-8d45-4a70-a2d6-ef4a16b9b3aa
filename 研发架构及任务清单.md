# GoChat Chrome 扩展研发架构及任务清单

## 项目概述

基于 Vue 3 + WXT + TailwindCSS v4 + Shadcn 的智能对话 Chrome 扩展，支持多模型切换、网页内容上下文感知、标签页级隔离的对话体验。

## 核心业务流程

### 1. 用户首次使用流程
```mermaid
flowchart TD
    A[用户安装扩展] --> B[打开SidePanel]
    B --> C[生成设备指纹]
    C --> D[创建匿名用户]
    D --> E[显示欢迎引导]
    E --> F[初始化配额: 10K tokens]
    F --> G[展示可用模型列表]
    G --> H[开始对话]

    H --> I{配额检查}
    I -->|充足| J[发送消息到AI]
    I -->|不足| K[显示升级提醒]

    J --> L[扣除配额]
    L --> M[显示AI回复]

    K --> N[引导用户注册]
    N --> O[数据迁移]
    O --> P[升级为注册用户]
```

### 2. 核心对话流程（最重要）
```mermaid
flowchart TD
    A[用户在SidePanel输入消息] --> B[Content Script提取页面内容]
    B --> C[解析页面结构]
    C --> D[提取关键信息]

    D --> E[页面标题]
    D --> F[主要文本内容]
    D --> G[选中文本]
    D --> H[页面URL和元数据]

    E --> I[构建上下文对象]
    F --> I
    G --> I
    H --> I

    I --> J[应用上下文压缩算法]
    J --> K[套用系统Prompt模板]

    K --> L[组装完整消息]
    L --> M{检查配额}
    M -->|不足| N[显示配额限制]
    M -->|充足| O[发送到AI代理服务]

    O --> P[后端验证用户权限]
    P --> Q[选择对应AI Provider]
    Q --> R[发送到AI模型]
    R --> S[接收流式响应]

    S --> T[实时显示回复]
    T --> U[计算Token消耗]
    U --> V[更新配额使用量]
    V --> W[保存对话记录]
    W --> X[更新会话状态]

    Y[用户继续对话] --> Z[保持上下文连续性]
    Z --> AA[合并历史消息]
    AA --> L
```

### 3. 标签页上下文感知流程
```mermaid
flowchart TD
    A[用户切换标签页] --> B[检测URL变化]
    B --> C[提取页面内容]
    C --> D{是否有该标签页的会话}
    D -->|有| E[恢复历史会话]
    D -->|无| F[创建新会话]

    E --> G[加载上下文]
    F --> G
    G --> H[用户发起对话]
    H --> I[组合消息: 用户输入 + 页面上下文]
    I --> J[发送到AI模型]
    J --> K[保存到会话历史]
    K --> L[显示回复]

    M[用户选择文本] --> N[右键菜单]
    N --> O[快速对话选项]
    O --> P[将选中文本作为重点上下文]
    P --> I
```

### 4. 多模型切换流程
```mermaid
flowchart TD
    A[用户点击模型选择器] --> B{检查用户类型}
    B -->|匿名用户| C[显示受限模型列表]
    B -->|注册用户| D[显示扩展模型列表]
    B -->|付费用户| E[显示全部模型列表]

    C --> F[GPT-3.5, Claude-3-Haiku]
    D --> G[+ GPT-4o-mini, Claude-3-Sonnet]
    E --> H[+ GPT-4, Claude-3-Opus, Gemini-Pro]

    F --> I{用户选择模型}
    G --> I
    H --> I

    I -->|可用模型| J[切换成功]
    I -->|受限模型| K[显示升级提示]

    J --> L[更新会话配置]
    L --> M[继续对话]

    K --> N[引导注册/升级]
```

### 5. 配额管理流程
```mermaid
flowchart TD
    A[用户发送消息] --> B[预估Token消耗]
    B --> C{检查当日配额}
    C -->|充足| D[发送请求]
    C -->|不足| E[配额超限提醒]

    D --> F[AI服务响应]
    F --> G[计算实际Token消耗]
    G --> H[更新配额使用量]
    H --> I[保存使用记录]

    E --> J{用户选择}
    J -->|注册升级| K[账户升级]
    J -->|等待重置| L[显示重置时间]
    J -->|购买配额| M[支付流程]

    K --> N[配额立即提升]
    N --> D

    O[每日0点] --> P[重置所有用户配额]
    P --> Q[发送配额重置通知]
```

### 6. 用户升级和数据迁移流程
```mermaid
flowchart TD
    A[匿名用户点击注册] --> B[OAuth认证]
    B --> C[获取用户信息]
    C --> D[创建注册用户记录]
    D --> E[开始数据迁移]

    E --> F[迁移对话历史]
    F --> G[迁移配额记录]
    G --> H[迁移用户偏好]
    H --> I[更新会话归属]
    I --> J[升级配额限制]
    J --> K[清理匿名数据]

    K --> L[迁移完成通知]
    L --> M[展示升级后功能]
    M --> N[继续使用]

    O[迁移失败] --> P[回滚操作]
    P --> Q[保持匿名状态]
    Q --> R[错误提示]
```

## 技术架构

### 核心技术栈
- **前端框架**: Vue 3.5 + Composition API + TypeScript
- **扩展框架**: WXT 0.20.7 (基于 Vite)
- **UI 框架**: TailwindCSS v4 + Reka UI (Shadcn Vue 版本)
- **状态管理**: Pinia + VueUse
- **数据存储**: RxDB (本地) + Chrome Storage API
- **路由**: Vue Router 4.5
- **国际化**: @wxt-dev/i18n
- **图标**: Lucide Vue Next + Iconify

### 扩展架构设计

#### 1. 入口点架构 (Entrypoints)
```
entrypoints/
├── background/           # 后台服务
│   ├── index.ts         # 主入口
│   ├── ai-service.ts    # AI 模型服务管理
│   ├── context-manager.ts # 网页内容管理
│   ├── auth-manager.ts  # 认证管理
│   └── billing-manager.ts # 计费管理
├── content/             # 内容脚本
│   ├── index.ts         # 主入口
│   ├── page-analyzer.ts # 页面内容分析
│   ├── selection-handler.ts # 文本选择处理
│   └── media-extractor.ts # 媒体资源提取
├── side-panel/          # 侧边栏主界面
│   ├── App.vue          # 主应用
│   ├── components/      # 组件
│   └── pages/           # 页面
├── popup/               # 弹窗界面
└── options/             # 设置页面
```

#### 2. 数据层架构

##### 本地存储架构 (RxDB)

**数据模型设计原则**：
- **会话隔离**：每个标签页对应独立的 ChatSession，支持并发多会话
- **用户身份兼容**：同时支持匿名用户（deviceId）和注册用户（userId）的数据关联
- **上下文完整性**：保存完整的页面上下文信息，支持会话恢复和历史回顾
- **配额追踪**：精确记录每条消息的 Token 消耗和成本信息

**核心数据实体**：
- **ChatSession**：会话主体，包含标签页绑定、模型配置、用户归属
- **Message**：消息记录，支持多角色（用户/助手/系统），包含成本追踪
- **PageContext**：页面上下文，包含 URL、内容、选中文本、媒体资源
- **UserPreferences**：用户偏好，包含模型选择、主题设置、自定义 Prompt
- **QuotaUsage**：配额使用记录，支持按日期、用户类型的精确统计

**数据关系约束**：
- **一对多关系**：一个会话包含多条消息，一个用户有多个配额记录
- **弱关联设计**：匿名用户数据可独立存在，注册后可无缝迁移
- **时间序列优化**：消息和配额数据按时间排序，支持高效的范围查询
- **存储容量管理**：自动清理过期数据，控制本地存储大小

##### Chrome Storage 同步
- 用户设置和 API 密钥
- 预置 Prompt 模板
- 主题和界面配置

#### 3. AI 服务架构

##### 多模型支持架构

**提供商抽象化设计**：
- **统一接口标准**：所有 AI 提供商遵循相同的接口规范，便于扩展和切换
- **认证机制多样化**：支持 API Key、OAuth、自定义认证等多种方式
- **模型能力映射**：标准化不同提供商的模型能力描述和限制
- **成本计算统一**：抽象化不同提供商的计费模式，提供统一的成本估算

**支持的主流提供商**：
- **OpenAI 系列**：GPT-4、GPT-3.5-turbo，支持函数调用和视觉理解
- **Anthropic Claude**：Claude-3 系列，擅长长文本理解和分析
- **Google Gemini**：多模态能力强，支持图像和代码理解
- **开源模型**：通过 Ollama 支持本地部署的开源模型
- **企业级服务**：Azure OpenAI、AWS Bedrock 等企业级 AI 服务

**模型选择策略**：
- **用户权限匹配**：根据用户类型自动过滤可用模型
- **任务适配推荐**：根据对话内容和页面类型推荐最适合的模型
- **成本效益平衡**：在模型能力和使用成本间找到最优平衡
- **性能监控**：实时监控各模型的响应时间和成功率

##### 流式响应处理架构
- **实时交互体验**：Server-Sent Events (SSE) 实现真正的流式对话
- **渐进式内容渲染**：支持 Markdown、代码块等复杂内容的实时渲染
- **网络容错机制**：断线重连、数据完整性校验、超时处理
- **用户控制能力**：随时停止生成、调整生成速度、内容过滤

#### 4. 上下文管理架构

##### 页面内容提取架构

**内容提取能力要求**：
- **智能文本提取**：识别并提取页面主要文本内容，过滤噪音信息
- **结构化信息解析**：提取标题层级、列表结构、表格数据等结构化信息
- **媒体资源识别**：识别图片、视频等媒体资源的描述信息和元数据
- **用户选择感知**：实时获取用户选中的文本内容作为重点上下文
- **动态变化监听**：监听页面内容变化，及时更新上下文信息

##### 标签页隔离机制
- **会话独立性**：每个标签页维护完全独立的对话会话和上下文
- **状态持久化**：标签页切换时自动保存和恢复会话状态
- **资源隔离**：不同标签页的配额使用、模型选择等完全隔离
- **跨页面关联**：支持相关页面间的上下文共享和引用

#### 5. 用户权限和配额架构

##### 用户分层权限体系

**三层用户类型设计**：
- **匿名用户 (Anonymous)**：无需注册即可使用，享受基础功能和有限配额
- **注册用户 (Registered)**：通过邮箱或第三方账号注册，获得扩展功能和更高配额
- **付费用户 (Premium)**：订阅付费服务，享受全功能和大配额

**配额分配策略**：
- **匿名用户限制**：每日 1 万 Token，仅可使用基础模型（GPT-3.5、Claude-3-Haiku）
- **注册用户提升**：每日 5 万 Token，可使用中级模型，支持上下文分析和 Prompt 保存
- **付费用户无限制**：每日 50 万 Token，可使用所有模型和功能

**功能权限矩阵**：
- **基础对话**：所有用户类型均可使用
- **上下文分析**：注册用户及以上
- **Prompt 管理**：注册用户及以上
- **高级模型**：付费用户专享
- **API 集成**：付费用户专享
- **数据导出**：付费用户专享

**配额重置机制**：
- **时间窗口**：每日 00:00:00 UTC 自动重置
- **滚动窗口**：支持按小时的滑动窗口限制，防止短时间内大量消耗
- **弹性配额**：根据用户历史使用模式动态调整配额上限
- **紧急配额**：为重要用户提供临时配额提升机制

##### 匿名用户识别策略

**多重标识符体系**：
- **设备指纹 (Device Fingerprint)**：基于浏览器环境特征生成的稳定标识
- **安装标识 (Install ID)**：扩展安装时生成的唯一标识，存储在本地
- **会话标识 (Session ID)**：当前浏览会话的临时标识，用于短期追踪
- **行为特征**：用户使用模式和偏好的行为指纹

**设备指纹生成原理**：
- **Canvas 指纹**：利用不同设备渲染差异生成唯一标识
- **硬件特征**：屏幕分辨率、颜色深度、时区等硬件环境信息
- **软件环境**：浏览器版本、操作系统、语言设置等软件特征
- **稳定性保障**：选择相对稳定的特征，避免频繁变化导致用户丢失

**隐私保护约束**：
- **非侵入性**：不收集个人敏感信息，仅使用公开的浏览器 API
- **可逆性控制**：用户可以重置标识，重新开始匿名使用
- **数据最小化**：只收集必要的识别信息，定期清理过期数据
- **透明度**：向用户明确说明数据收集和使用方式

##### Chrome 扩展权限策略

**核心权限需求**：
- **sidePanel**：侧边栏界面的基础权限，用于主要交互界面
- **tabs + activeTab**：获取标签页信息和内容，实现上下文感知
- **storage**：本地数据存储，保存对话历史和用户偏好
- **contextMenus**：右键菜单集成，提供快捷操作入口
- **identity**：用户身份认证，支持第三方登录
- **scripting**：内容脚本注入，实现页面内容提取

**网络访问权限**：
- **全域名访问**：支持所有 HTTP/HTTPS 网站的内容提取
- **API 端点访问**：连接后端服务和 AI 提供商 API
- **安全限制**：遵循 CSP 策略，防止恶意脚本注入

##### 安全措施
- 设备指纹 + 安装ID 双重匿名用户识别
- API 密钥服务端代理，客户端不存储
- 内容脚本沙箱隔离
- CSP 策略配置
- 配额数据加密存储

## 核心业务实现架构

### 1. 匿名用户支持架构

#### 1.1 匿名用户生命周期管理

**初始化阶段设计**：
- **首次访问检测**：识别新用户并触发初始化流程
- **设备指纹生成**：创建稳定的匿名用户标识
- **配额分配**：为新用户分配初始配额和权限
- **引导体验**：提供友好的首次使用引导

**配额管理机制**：
- **实时监控**：每次 AI 请求前检查配额余量
- **精确计费**：基于实际 Token 消耗更新配额使用量
- **超限处理**：优雅处理配额不足的情况，提供升级引导
- **使用统计**：记录详细的使用历史，支持分析和优化

**用户升级流程**：
- **无缝迁移**：匿名用户数据完整迁移到注册账户
- **权限提升**：自动升级配额限制和功能权限
- **历史保留**：保持对话历史和用户偏好的连续性
- **回滚机制**：升级失败时的数据回滚和错误处理

**生命周期事件**：
- **创建事件**：记录用户首次使用时间和来源
- **活跃追踪**：更新用户最后活跃时间
- **升级事件**：记录用户升级时间和方式
- **清理机制**：定期清理长期不活跃的匿名用户数据

#### 1.2 配额限制执行策略

**多维度权限控制**：
- **模型访问控制**：根据用户类型限制可用的 AI 模型
- **功能权限验证**：控制高级功能的访问权限
- **Token 配额管理**：精确控制每日 Token 使用量
- **并发请求限制**：防止用户同时发起过多请求

**实时权限验证**：
- **请求前检查**：每次 AI 请求前验证用户权限和配额
- **动态权限调整**：根据用户行为和系统负载动态调整权限
- **异常处理机制**：优雅处理权限不足和配额超限的情况
- **用户友好提示**：提供清晰的错误信息和升级建议

**配额超限处理策略**：
- **软限制机制**：接近配额上限时提前警告用户
- **硬限制执行**：超出配额时阻止请求并提供明确说明
- **紧急配额**：为特殊情况提供临时配额提升
- **升级引导**：配额不足时引导用户注册或升级

**防滥用机制**：
- **频率限制**：限制单位时间内的请求频率
- **异常检测**：识别异常使用模式和潜在滥用行为
- **IP 级限制**：对同一 IP 下的设备数量进行限制
- **行为分析**：基于用户行为模式进行风险评估

#### 1.3 服务端代理架构

**API 代理服务设计**：
- **统一入口**：所有 AI 请求通过统一的代理服务处理
- **身份验证**：支持匿名用户和注册用户的双重验证机制
- **配额控制**：在服务端层面执行严格的配额限制
- **使用记录**：精确记录每次请求的 Token 消耗和成本

**请求处理流程**：
- **预处理阶段**：验证用户身份、检查配额、预估成本
- **代理转发**：将请求转发到相应的 AI 提供商
- **响应处理**：处理 AI 响应、计算实际消耗、更新配额
- **后处理**：记录使用日志、更新统计数据、触发相关事件

**安全性保障**：
- **API 密钥保护**：客户端不存储任何 API 密钥，全部在服务端管理
- **请求验证**：验证请求来源和完整性，防止伪造请求
- **速率限制**：在服务端层面实施速率限制和防 DDoS 保护
- **数据加密**：敏感数据的传输和存储加密

**成本控制机制**：
- **实时监控**：监控各 AI 提供商的使用量和成本
- **成本预警**：设置成本阈值和预警机制
- **智能路由**：根据成本和性能选择最优的 AI 提供商
- **预算管理**：为不同用户类型设置预算限制

### 2. 核心对话系统架构

#### 2.1 页面内容提取器

##### 推荐开源库方案
1. **Mozilla Readability** (主要方案)
   - Firefox Reader Mode 的核心算法
   - 专门用于提取文章主要内容，支持中文
   - 能识别正文、标题、作者等结构化信息
   - 准确率高，适合新闻、博客等内容型网站

2. **Boilerpipe** (备选方案)
   - 专门用于去除网页"样板"内容
   - 算法成熟，在学术界广泛使用
   - 适合处理复杂布局的网站

3. **自定义智能提取器** (兜底方案)
   - 多策略组合：语义化选择器 + 启发式算法
   - 针对不同网站类型的专门策略
   - 内容质量评估和置信度计算

##### 多层级提取策略设计

**策略优先级原则**：
1. **高精度优先**：Mozilla Readability 作为首选，置信度阈值 > 0.8
2. **语义化降级**：基于 HTML5 语义标签和常见 CSS 类名，置信度阈值 > 0.6
3. **启发式兜底**：基于文本密度和结构分析的算法保底

**网站类型适配策略**：
- **新闻媒体网站**：优先识别文章正文区域，过滤广告和推荐内容
- **博客和个人网站**：重点提取文章内容，保留作者信息和发布时间
- **电商网站**：专注产品描述和规格信息，过滤评论和推荐
- **技术文档**：保留代码块和结构化信息，维持层级关系
- **中文网站**：考虑中文网站的布局特点和命名习惯

##### 噪音过滤机制

**导航和结构元素过滤**：
- **设计约束**：必须保留页面主要内容的完整性，不能误删正文
- **过滤范围**：网站导航、面包屑、侧边栏、页脚信息
- **判断依据**：基于 HTML 语义标签（nav, header, footer, aside）和常见 CSS 类名
- **特殊处理**：文章内的导航链接（如目录）需要保留

**广告和推广内容识别**：
- **识别策略**：基于 CSS 类名模式匹配（ad, ads, advertisement, promo, sponsored）
- **行为分析**：检测异步加载的广告内容和第三方嵌入
- **尺寸约束**：过滤明显的广告尺寸区域（如 300x250, 728x90 等标准广告位）
- **内容特征**：识别推广性语言和外链密度异常的区域

**社交和互动组件处理**：
- **过滤对象**：评论区、社交分享按钮、相关推荐、用户互动区域
- **保留原则**：与主要内容强相关的用户生成内容可选择性保留
- **动态内容**：处理 JavaScript 动态加载的社交组件
- **隐私考虑**：避免提取用户个人信息和敏感互动数据

**脚本和样式清理**：
- **完全移除**：所有 script、style、noscript 标签及其内容
- **隐藏元素**：CSS display:none 或 visibility:hidden 的内容
- **无障碍内容**：保留 aria-label 等无障碍访问信息
- **结构化数据**：可选择性保留 JSON-LD 等结构化数据

##### 内容质量评估体系

**置信度计算维度**：
- **文本长度权重**：200-1000字符为基础分，超过1000字符加分，少于200字符扣分
- **结构完整性**：段落数量、标题层级、列表结构的完整性评估
- **语义丰富度**：专业术语密度、信息密度、表达完整性
- **噪音比例**：链接密度、重复内容比例、无意义字符比例

**内容完整性评估**：
- **逻辑连贯性**：段落间的逻辑关系，是否存在明显的内容断层
- **信息密度**：单位文本中包含的有效信息量
- **上下文完整性**：是否包含足够的背景信息供 AI 理解
- **多媒体关联**：文本与图片、表格等多媒体内容的关联度

**可读性评估标准**：
- **段落结构**：合理的段落划分，避免过长或过短的段落
- **语言复杂度**：句子长度、词汇复杂度、专业术语比例
- **格式规范性**：标点符号使用、空格处理、编码正确性
- **信息层次**：标题、子标题、列表等层次结构的清晰度

**动态质量调整机制**：
- **网站信誉度**：基于域名权威性和内容历史质量调整基础分数
- **用户反馈**：结合用户对提取内容质量的反馈进行算法优化
- **AI 理解度**：基于 AI 模型对内容的理解程度调整质量评分
- **实时监控**：监控提取失败率和用户满意度，动态调整策略参数

#### 2.2 上下文压缩策略

##### 智能压缩原则
**信息优先级排序**：
- **核心信息保留**：页面标题、主要段落、关键数据必须保留
- **次要信息压缩**：示例代码、引用内容、补充说明可适度压缩
- **冗余信息删除**：重复段落、无关链接、格式化字符优先删除

**Token 预算管理**：
- **基础配额**：为系统 Prompt 预留 500-800 tokens
- **历史对话**：为对话历史预留 1000-2000 tokens
- **页面内容**：剩余空间分配给页面内容，通常 2000-4000 tokens
- **用户输入**：为用户当前输入预留 200-500 tokens

**多语言处理约束**：
- **中文 Token 估算**：平均 1.5 字符/token，考虑词汇复杂度变化
- **英文 Token 估算**：平均 4 字符/token，技术文档可能更密集
- **混合语言处理**：分别计算后合并，避免估算偏差
- **特殊字符处理**：代码、公式、符号需要特殊处理规则

#### 2.3 Prompt 管理和对话系统设计

##### Prompt 模板引擎架构
**模板系统设计原则**：
- **变量替换机制**：支持动态变量如 {{pageTitle}}、{{pageContent}}、{{selectedText}}
- **条件渲染逻辑**：根据上下文条件动态包含或排除内容块
- **模板继承体系**：基础模板 + 场景特化模板的层次结构
- **多语言支持**：支持中英文等多语言的 Prompt 模板

**场景化模板库**：
- **默认对话模板**：通用的网页助手角色定义和行为指导
- **内容分析模板**：专门用于网页内容的结构化分析
- **摘要提取模板**：快速生成页面内容摘要的专用模板
- **问答增强模板**：基于页面内容回答用户问题的优化模板
- **代码解释模板**：针对技术文档和代码页面的专用模板

##### 对话上下文管理策略
**消息历史处理**：
- **智能截断算法**：基于相关性而非时间顺序的历史消息选择
- **上下文窗口管理**：动态调整历史消息数量以适应不同模型的限制
- **关键信息保留**：确保重要的上下文信息不被截断丢失
- **话题连续性维护**：识别和保持对话主题的连贯性

**消息组装流程**：
- **系统消息构建**：基于页面内容和用户类型生成系统级指令
- **历史消息筛选**：智能选择相关的历史对话作为上下文
- **用户消息增强**：结合选中文本和页面信息增强用户输入
- **Token 预算分配**：在系统消息、历史消息、用户输入间合理分配 Token

##### 流式对话实现机制
**请求构建策略**：
- **消息格式标准化**：统一不同 AI 提供商的消息格式要求
- **参数配置管理**：温度、最大长度、停止词等参数的智能配置
- **错误处理预案**：网络异常、API 限制、内容过滤等异常情况处理
- **重试机制设计**：指数退避的智能重试策略

**响应处理流程**：
- **流式数据解析**：实时解析 SSE 数据流，处理不完整的 JSON
- **内容增量更新**：高效的 DOM 更新策略，避免频繁重渲染
- **完成状态检测**：准确识别响应完成和异常中断
- **Token 使用统计**：精确统计实际消耗的 Token 数量

#### 2.4 消息组装策略

##### 上下文组合原则
**信息层次化组织**：
- **系统级上下文**：页面基础信息、用户类型、模型能力说明
- **页面级上下文**：当前页面的主要内容和结构化信息
- **会话级上下文**：历史对话记录和用户偏好
- **请求级上下文**：用户当前输入和选中的特定内容

**历史对话管理策略**：
- **智能截断**：基于对话相关性而非简单的数量限制
- **重要信息保留**：关键的上下文信息和用户偏好设置
- **话题连续性**：保持当前话题的完整对话链
- **性能平衡**：在上下文丰富度和响应速度间找到平衡

**动态优先级调整**：
- **用户选中文本优先**：当用户选中特定内容时，提高其在上下文中的权重
- **页面更新感知**：检测页面内容变化，及时更新上下文
- **模型能力适配**：根据不同 AI 模型的上下文窗口大小调整策略
- **实时性要求**：平衡信息完整性和响应速度的需求

#### 2.5 流式响应处理机制

##### 实时交互体验设计
**渐进式内容显示**：
- **打字机效果**：逐字符显示 AI 回复，提供真实的对话感受
- **内容预处理**：实时处理 Markdown 格式、代码块、链接等特殊内容
- **响应中断机制**：用户可随时停止生成，避免不必要的资源消耗
- **错误恢复**：网络中断时的自动重连和内容恢复机制

**性能优化约束**：
- **缓冲区管理**：合理的数据缓冲，避免频繁的 DOM 更新
- **内存控制**：长对话时的内存使用优化，防止浏览器卡顿
- **网络效率**：最小化网络请求开销，优化数据传输格式
- **并发限制**：同时只允许一个活跃的流式请求

**用户体验保障**：
- **加载状态指示**：清晰的加载动画和进度提示
- **响应时间监控**：超时检测和用户友好的错误提示
- **内容完整性**：确保流式传输不会丢失或损坏内容
- **可访问性支持**：屏幕阅读器等辅助技术的兼容性

#### 2.6 对话持续性管理策略

##### 会话生命周期管理
**标签页级隔离原则**：
- **独立上下文**：每个标签页维护独立的对话上下文和历史
- **状态同步**：标签页切换时的会话状态恢复和同步
- **资源清理**：标签页关闭时的内存和存储资源清理
- **跨会话关联**：相关页面间的上下文关联和信息共享

**历史消息管理策略**：
- **智能截断**：基于对话相关性和重要性的智能历史管理
- **关键信息保留**：用户偏好、重要决策、上下文关键点的永久保存
- **存储优化**：本地存储和云端同步的平衡策略
- **隐私保护**：敏感信息的自动识别和保护机制

**上下文连贯性保障**：
- **话题追踪**：识别和维护对话主题的连续性
- **引用解析**：处理代词引用和上下文依赖的表达
- **时间感知**：考虑时间因素对对话上下文的影响
- **页面变化适应**：页面内容更新时的上下文动态调整

##### 性能和存储约束
**内存使用优化**：
- **懒加载策略**：按需加载历史对话，避免内存浪费
- **缓存管理**：热点会话的智能缓存和过期策略
- **数据压缩**：历史消息的压缩存储和快速检索
- **垃圾回收**：定期清理无用数据和过期会话

**数据持久化策略**：
- **本地优先**：优先使用本地存储，确保离线可用性
- **增量同步**：只同步变更部分，减少网络开销
- **冲突解决**：多设备间数据冲突的自动解决机制
- **备份恢复**：重要对话数据的备份和恢复机制

### 3. 前端用户体验设计

#### 3.1 匿名用户引导体验设计

**首次使用引导策略**：
- **欢迎对话框**：简洁明了地介绍产品核心功能和价值
- **功能演示**：通过交互式演示展示主要功能
- **权限说明**：清晰说明匿名用户的权限和限制
- **升级路径**：明确展示注册后的额外权益

**配额状态可视化**：
- **实时显示**：在界面显著位置实时显示配额使用情况
- **状态分级**：通过颜色和图标区分正常、警告、危险状态
- **趋势预测**：基于使用模式预测配额耗尽时间
- **升级引导**：配额不足时提供明确的升级入口

**渐进式功能开放**：
- **功能预览**：让用户看到高级功能但标明需要升级
- **试用机制**：提供有限的高级功能试用机会
- **对比展示**：清晰对比不同用户类型的功能差异
- **价值传达**：突出升级后的具体价值和收益

**用户体验优化**：
- **非侵入式提醒**：避免过度打扰用户的正常使用
- **个性化推荐**：根据用户使用模式推荐合适的升级方案
- **社会证明**：展示其他用户的升级反馈和使用效果
- **简化流程**：最小化升级注册的步骤和复杂度

#### 3.2 智能模型选择体验

**权限可视化设计**：
- **分层展示**：清晰区分可用模型、受限模型和付费模型
- **状态标识**：通过图标和标签明确标识模型的访问状态
- **能力说明**：简洁描述每个模型的特点和适用场景
- **成本透明**：显示不同模型的相对成本和 Token 消耗

**交互体验优化**：
- **智能推荐**：根据对话内容和页面类型推荐最适合的模型
- **一键切换**：支持在对话过程中快速切换模型
- **历史记忆**：记住用户的模型偏好和使用习惯
- **性能提示**：显示模型的响应速度和质量评级

**升级引导集成**：
- **锁定状态**：受限模型显示锁定图标和升级提示
- **试用机会**：为匿名用户提供有限的高级模型试用
- **价值对比**：突出高级模型的能力优势
- **无缝升级**：点击受限模型直接跳转到升级流程

**用户教育**：
- **模型介绍**：提供详细的模型能力和特点说明
- **使用建议**：根据任务类型推荐最适合的模型
- **效果展示**：通过示例展示不同模型的输出差异
- **最佳实践**：分享模型使用的最佳实践和技巧

### 4. 核心业务系统架构

#### 4.1 对话管理系统
**会话生命周期管理**：
- **会话创建**：基于标签页自动创建独立的对话会话
- **状态维护**：实时维护会话状态、模型配置和用户偏好
- **消息处理**：处理用户输入、AI 响应和系统消息
- **会话持久化**：自动保存会话数据，支持跨设备同步

**智能对话控制**：
- **上下文管理**：智能管理对话上下文，保持连贯性
- **模型切换**：支持对话过程中的无缝模型切换
- **配额预检**：发送前检查配额，避免请求失败
- **错误恢复**：网络异常时的自动重试和恢复机制

#### 4.2 上下文感知系统
**页面内容感知**：
- **实时提取**：动态提取当前页面的主要内容和结构
- **选择感知**：实时获取用户选中的文本作为重点上下文
- **媒体识别**：识别页面中的图片、视频等媒体资源信息
- **变化监听**：监听页面内容变化，及时更新上下文

**智能上下文处理**：
- **内容压缩**：智能压缩页面内容，保留关键信息
- **相关性分析**：分析内容与用户问题的相关性
- **多模态融合**：整合文本、图片、结构化数据等多种信息
- **历史关联**：关联历史对话中的相关上下文

#### 4.3 AI 模型管理系统
**提供商管理**：
- **多提供商支持**：统一管理多个 AI 服务提供商
- **动态注册**：支持运行时动态添加新的 AI 提供商
- **健康检查**：定期检查各提供商的服务状态和可用性
- **负载均衡**：在多个提供商间智能分配请求负载

**模型能力管理**：
- **能力映射**：标准化描述不同模型的能力和限制
- **性能监控**：实时监控各模型的响应时间和质量
- **成本优化**：根据成本和性能选择最优模型
- **版本管理**：管理模型版本更新和兼容性

#### 4.4 配额和计费系统

**配额管理架构**：
- **分层配额控制**：支持匿名用户、注册用户、付费用户的差异化配额
- **实时配额监控**：精确追踪每个用户的配额使用情况
- **智能配额分配**：根据用户行为和系统负载动态调整配额
- **配额重置机制**：自动化的每日配额重置和历史数据清理

**匿名用户特殊处理**：
- **设备级配额**：基于设备指纹的配额管理和追踪
- **防滥用机制**：检测和防止匿名用户的恶意使用
- **升级引导**：配额不足时的智能升级引导
- **数据迁移**：匿名用户升级时的配额数据迁移

**计费和统计系统**：
- **精确计费**：基于实际 Token 消耗的精确计费
- **成本分析**：详细的成本分析和优化建议
- **使用统计**：丰富的使用数据统计和可视化
- **预算控制**：用户级和系统级的预算控制机制

**支付和订阅管理**：
- **多种支付方式**：支持信用卡、PayPal、加密货币等支付方式
- **订阅管理**：灵活的订阅计划和自动续费机制
- **退款处理**：完善的退款政策和处理流程
- **发票管理**：自动生成和发送使用发票

## 直接使用 AI SDK 的实现方案

### 1. Prompt 模板引擎实现

#### 模板变量系统
**变量定义和替换**：
- **页面变量**：`{{pageTitle}}`、`{{pageUrl}}`、`{{domain}}`、`{{pageContent}}`
- **用户变量**：`{{selectedText}}`、`{{userInput}}`、`{{userType}}`
- **系统变量**：`{{currentTime}}`、`{{model}}`、`{{language}}`
- **动态变量**：基于页面类型和内容特征的动态变量

**条件渲染逻辑**：
- **存在性判断**：`{{#if selectedText}}...{{/if}}` 仅在有选中文本时显示
- **用户类型判断**：`{{#if isPremium}}...{{/if}}` 根据用户类型显示不同内容
- **内容类型判断**：`{{#if isArticle}}...{{/if}}` 根据页面类型调整 Prompt
- **嵌套条件**：支持复杂的嵌套条件逻辑

#### 模板库设计
**基础模板结构**：
```
系统角色定义 + 当前页面信息 + 用户上下文 + 任务指令 + 输出格式要求
```

**模板分类体系**：
- **通用助手模板**：适用于一般性对话和问答
- **内容分析模板**：专门用于页面内容的深度分析
- **摘要生成模板**：快速提取和总结页面核心信息
- **代码解释模板**：针对技术文档和代码的专用模板
- **翻译优化模板**：多语言内容的翻译和本地化

### 2. 对话上下文管理

#### 消息历史处理策略
**智能截断算法**：
- **相关性评分**：基于语义相似度计算历史消息的相关性
- **重要性权重**：用户明确提及的信息、系统重要提示等高权重保留
- **时间衰减因子**：较新的消息获得更高的保留优先级
- **话题连续性**：保持当前话题相关的完整对话链

**上下文窗口管理**：
- **动态窗口大小**：根据不同模型的上下文限制动态调整
- **预留空间策略**：为系统 Prompt 和用户输入预留足够空间
- **压缩策略**：对较老的消息进行摘要压缩而非直接删除
- **关键信息提取**：从被截断的消息中提取关键信息保留

#### 消息组装流程
**分层消息构建**：
1. **系统消息层**：角色定义、页面信息、行为约束
2. **上下文消息层**：页面内容摘要、用户偏好设置
3. **历史对话层**：经过筛选和压缩的历史消息
4. **当前请求层**：用户当前输入和选中内容

**Token 预算分配**：
- **系统消息**：30-40% 的 Token 预算
- **页面内容**：25-35% 的 Token 预算
- **历史对话**：20-30% 的 Token 预算
- **用户输入**：10-15% 的 Token 预算

### 3. 基于 OpenRouter.ai 的统一模型访问

#### 聚合 API 服务优势
**OpenRouter.ai 集成方案**：
- **统一接口**：使用 OpenAI 兼容格式访问所有主流模型
- **模型聚合**：GPT-4、Claude、Gemini、Llama 等模型的统一访问
- **自动负载均衡**：在多个提供商间智能分配请求
- **成本优化**：自动选择性价比最高的模型实例

**简化的架构设计**：
- **单一 SDK**：只需 OpenAI SDK，无需多个提供商的适配器
- **统一计费**：一个账户管理所有模型的使用和计费
- **统一监控**：集中的使用统计、成本分析和性能监控
- **统一错误处理**：标准化的错误格式和处理机制

#### 模型选择和路由策略
**智能模型路由**：
- **任务适配**：根据对话类型自动推荐最适合的模型
- **成本控制**：在质量和成本间找到最优平衡点
- **性能优化**：根据响应时间和可用性选择模型
- **用户偏好**：记住用户的模型偏好和使用习惯

#### 流式响应处理
**数据流解析**：
- **SSE 格式处理**：解析 Server-Sent Events 数据流
- **JSON 增量解析**：处理不完整的 JSON 数据块
- **错误检测**：识别和处理流式传输中的错误
- **完成检测**：准确识别响应完成的各种信号

**实时 UI 更新**：
- **增量渲染**：只更新变化的部分，避免全量重渲染
- **Markdown 实时解析**：支持代码块、列表等复杂格式的实时渲染
- **打字机效果**：平滑的逐字符显示效果
- **用户控制**：支持暂停、停止、调速等用户控制

### 4. 错误处理和重试机制

#### 多层错误处理
**网络层错误**：
- **连接超时**：设置合理的连接和读取超时时间
- **网络中断**：检测网络状态变化，自动重连
- **限流处理**：识别 API 限流，实施指数退避重试
- **服务不可用**：自动切换到备用提供商或降级服务

**API 层错误**：
- **认证失败**：API 密钥验证失败的处理和用户提示
- **配额超限**：API 配额不足时的优雅降级
- **内容过滤**：内容被安全过滤时的用户友好提示
- **格式错误**：请求格式不正确时的自动修正

#### 用户体验保障
**错误用户界面**：
- **友好错误提示**：将技术错误转换为用户易懂的说明
- **重试选项**：为用户提供手动重试的选项
- **降级方案**：在高级功能不可用时提供基础功能
- **帮助指导**：提供解决问题的具体指导步骤

## 关键开源库选择

### UI 组件库
- **Reka UI**: Vue 版本的 Radix UI，提供无样式的可访问组件
- **Lucide Vue Next**: 现代图标库
- **Vue Sonner**: 优雅的通知组件

### 数据管理
- **RxDB**: 响应式本地数据库，支持离线和同步
- **Pinia**: Vue 3 官方状态管理
- **VueUse**: Vue 组合式 API 工具集

### 网络请求
- **Fetch API**: 原生 HTTP 客户端
- **EventSource**: SSE 流式响应处理

### AI 服务库选择分析

#### 推荐方案：OpenRouter.ai + 自建轻量级 SDK
- **openai**: OpenAI 官方 SDK，通过 OpenRouter.ai 访问所有主流模型
- **OpenRouter.ai**: 多模型聚合服务，统一 API 接口和计费
- **自建 AI 工具库**: 借鉴 Vercel AI SDK 设计理念的轻量级实现

#### 不推荐的替代方案
- **Vercel AI SDK**: 功能强大但体积较大（2-3MB），主要为 Next.js 优化
- **LangChain**: 功能最全但体积巨大（50MB+），对扩展来说过于复杂
- **直接多 SDK 集成**: 需要处理多个不同的 API 格式，维护成本高

#### 借鉴 Vercel AI SDK 的设计理念
- **统一的流式接口**: 学习其流式响应的处理方式
- **类型安全**: 借鉴其 TypeScript 类型定义的设计
- **错误处理**: 参考其统一的错误处理机制
- **工具函数**: 学习其 Token 计算、消息格式化等工具函数

### 内容提取库
- **@mozilla/readability**: Firefox Reader Mode 核心算法，专门提取文章主要内容
- **boilerpipe**: 去除网页"样板"内容的成熟算法
- **自定义智能提取器**: 多策略组合方案，支持语义化选择器 + 启发式算法

### 工具库
- **Zod**: TypeScript 优先的数据验证
- **date-fns**: 现代日期处理库
- **cuid**: 唯一 ID 生成器

### 开发工具
- **WXT**: 现代扩展开发框架
- **Vite**: 快速构建工具
- **TypeScript**: 类型安全
- **TailwindCSS**: 原子化 CSS 框架

## 详细任务清单

### 阶段一：基础架构搭建 (1-2 周)

#### 1.1 项目结构优化
- [ ] 重构现有目录结构
- [ ] 配置 TypeScript 严格模式
- [ ] 设置 ESLint + Prettier
- [ ] 配置 Vite 构建优化

#### 1.2 用户系统设计
- [ ] 实现匿名用户识别机制
- [ ] 设计设备指纹生成算法
- [ ] 创建用户类型管理系统
- [ ] 实现配额管理架构

#### 1.3 数据库设计
- [ ] 设计 RxDB Schema (ChatSession, Message, UserPreferences, QuotaUsage)
- [ ] 实现数据库迁移策略
- [ ] 创建数据访问层 (DAL)
- [ ] 实现数据同步机制

#### 1.4 基础组件开发
- [ ] 创建聊天界面组件
- [ ] 实现消息气泡组件
- [ ] 开发增强型模型选择器（支持权限显示）
- [ ] 创建配额状态指示器
- [ ] 实现用户引导组件
- [ ] 开发升级提醒对话框
- [ ] 创建设置面板组件

### 阶段二：核心功能实现 (2-3 周)

#### 2.1 AI 服务集成
- [ ] 设计和实现 Prompt 模板引擎
- [ ] 创建模板变量系统和条件渲染逻辑
- [ ] 建立分类化的 Prompt 模板库
- [ ] 实现智能消息历史截断算法
- [ ] 开发上下文窗口动态管理机制
- [ ] 集成 OpenRouter.ai 聚合服务
- [ ] 配置多模型支持（GPT-4、Claude、Gemini 等）
- [ ] 实现模型选择和智能路由逻辑
- [ ] 开发流式响应处理机制
- [ ] 实现统一的错误处理和重试策略
- [ ] 创建成本监控和优化机制

#### 2.2 核心对话系统
- [ ] 集成 Mozilla Readability 库
- [ ] 实现多策略内容提取器（Readability + 选择器 + 启发式）
- [ ] 开发针对不同网站类型的提取策略
- [ ] 创建内容质量评估算法
- [ ] 开发智能内容压缩算法
- [ ] 创建系统 Prompt 模板引擎
- [ ] 实现消息组装和上下文管理
- [ ] 开发流式响应处理机制
- [ ] 创建对话持续性管理器

#### 2.3 上下文管理
- [ ] 实现页面结构化解析
- [ ] 开发文本选择监听
- [ ] 创建媒体资源提取器
- [ ] 实现标签页隔离机制
- [ ] 添加上下文压缩和优化

#### 2.4 会话管理系统
- [ ] 实现标签页级会话隔离
- [ ] 开发会话状态持久化
- [ ] 创建对话历史存储和检索
- [ ] 实现会话导出功能
- [ ] 添加对话搜索功能
- [ ] 实现匿名用户数据迁移机制

### 阶段三：高级功能开发 (2-3 周)

#### 3.1 Prompt 管理
- [ ] 设计 Prompt 模板系统
- [ ] 实现预置 Prompt 库
- [ ] 开发自定义 Prompt 编辑器
- [ ] 创建 Prompt 分享机制
- [ ] 实现 Prompt 变量替换

#### 3.2 用户体验优化
- [ ] 实现主题切换
- [ ] 添加快捷键支持
- [ ] 开发拖拽调整界面
- [ ] 实现消息编辑功能
- [ ] 添加打字机效果

#### 3.3 右键菜单集成
- [ ] 实现图片右键分析
- [ ] 添加视频信息提取
- [ ] 创建文本快速对话
- [ ] 开发链接内容分析

### 阶段四：认证和计费 (1-2 周)

#### 4.1 用户认证系统
- [ ] 集成 OAuth 2.0 认证
- [ ] 实现用户会话管理
- [ ] 开发匿名用户升级机制
- [ ] 实现账户数据迁移
- [ ] 添加多设备支持

#### 4.2 配额和计费系统
- [ ] 实现匿名用户配额管理
- [ ] 开发 Token 使用统计
- [ ] 创建配额超限提醒
- [ ] 实现配额重置机制
- [ ] 集成支付网关
- [ ] 创建使用报告和分析

### 阶段五：测试和优化 (1 周)

#### 5.1 测试覆盖
- [ ] 单元测试 (Vitest)
- [ ] 集成测试
- [ ] E2E 测试 (Playwright)
- [ ] 性能测试

#### 5.2 性能优化
- [ ] 代码分割优化
- [ ] 内存使用优化
- [ ] 网络请求优化
- [ ] 缓存策略实现

#### 5.3 发布准备
- [ ] 构建流程优化
- [ ] 文档编写
- [ ] Chrome Web Store 准备
- [ ] 用户手册制作

## 开发规范

### 代码规范
- 使用 TypeScript 严格模式
- 遵循 Vue 3 Composition API 最佳实践
- 采用函数式编程风格
- 实现完整的错误处理

### 提交规范
- 使用 Conventional Commits
- 每个功能独立分支开发
- 代码审查后合并主分支

### 测试策略
- 单元测试覆盖率 > 80%
- 关键路径集成测试
- 用户体验 E2E 测试

## 匿名用户支持的技术实现要点

### 1. 设备指纹稳定性
- **挑战**: 浏览器更新、隐私模式可能导致指纹变化
- **解决方案**: 多重标识符组合 + 模糊匹配算法
- **备选方案**: Chrome Extension Storage API 存储持久化ID

### 2. 配额防刷机制
- **IP限制**: 同一IP每日最大设备数限制
- **行为分析**: 异常使用模式检测
- **时间窗口**: 滑动窗口配额控制
- **设备验证**: 浏览器环境一致性检查

### 3. 数据迁移策略设计

**迁移范围和原则**：
- **完整性保障**：确保所有用户数据完整迁移，无遗漏
- **一致性维护**：保持数据关系和约束的一致性
- **原子性操作**：迁移过程要么全部成功，要么全部回滚
- **最小停机时间**：迁移过程中最小化对用户体验的影响

**分阶段迁移流程**：
- **对话历史迁移**：将匿名用户的所有对话会话转移到注册账户
- **配额记录合并**：合并匿名使用记录到注册用户的配额历史
- **偏好设置同步**：迁移用户的个性化设置和偏好配置
- **清理和验证**：清理匿名数据并验证迁移结果的正确性

**错误处理和回滚**：
- **事务性操作**：使用数据库事务确保操作的原子性
- **备份机制**：迁移前创建数据备份，支持快速回滚
- **错误监控**：实时监控迁移过程，及时发现和处理异常
- **用户通知**：迁移失败时及时通知用户并提供解决方案

**性能优化**：
- **批量处理**：对大量数据采用批量处理提高效率
- **异步执行**：非关键迁移任务异步执行，避免阻塞用户
- **进度反馈**：为用户提供迁移进度的实时反馈
- **资源控制**：控制迁移过程的资源使用，避免影响系统性能

### 4. 前端状态管理架构

**全局状态设计**：
- **用户状态管理**：统一管理用户类型、身份信息和权限状态
- **配额状态追踪**：实时追踪和显示用户的配额使用情况
- **会话状态维护**：管理多个标签页的会话状态和切换
- **UI 状态控制**：控制界面元素的显示、隐藏和交互状态

**响应式数据流**：
- **状态响应性**：使用 Vue 3 的响应式系统确保状态变化的实时反映
- **计算属性**：基于基础状态计算衍生状态，如剩余配额、权限判断等
- **状态持久化**：关键状态的本地持久化，确保页面刷新后的状态恢复
- **跨组件通信**：通过状态管理实现组件间的数据共享和通信

**状态更新策略**：
- **乐观更新**：UI 先行更新，后台异步同步，提升用户体验
- **错误回滚**：操作失败时自动回滚状态到之前的正确状态
- **批量更新**：合并多个状态更新，减少不必要的重渲染
- **状态验证**：关键状态变更时的合法性验证和约束检查

**性能优化**：
- **懒加载**：非关键状态的懒加载，减少初始化时间
- **缓存策略**：合理缓存计算结果，避免重复计算
- **内存管理**：及时清理不再需要的状态数据，防止内存泄漏
- **更新优化**：精确控制状态更新的范围，最小化重渲染影响

## 风险评估

### 技术风险
- Chrome API 变更风险 - 中等
- AI 服务稳定性风险 - 中等
- 设备指纹准确性风险 - 中等
- 配额防刷绕过风险 - 中等
- 性能优化挑战 - 低

### 业务风险
- 用户隐私合规 - 高
- API 成本控制 - 高（匿名用户增加成本控制难度）
- 免费额度滥用 - 中等
- 竞品压力 - 中等

## 后续扩展计划

### 功能扩展
- 支持更多 AI 模型
- 添加语音对话功能
- 实现协作对话功能
- 开发移动端支持

### 平台扩展
- Firefox 扩展版本
- Safari 扩展版本
- Edge 扩展版本
- 独立 Web 应用

---

*本文档将随着开发进度持续更新和完善*
