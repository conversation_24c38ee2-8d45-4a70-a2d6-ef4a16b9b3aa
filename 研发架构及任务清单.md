# GoChat Chrome 扩展研发架构及任务清单

## 项目概述

基于 Vue 3 + WXT + TailwindCSS v4 + Shadcn 的智能对话 Chrome 扩展，支持多模型切换、网页内容上下文感知、标签页级隔离的对话体验。

## 技术架构

### 核心技术栈
- **前端框架**: Vue 3.5 + Composition API + TypeScript
- **扩展框架**: WXT 0.20.7 (基于 Vite)
- **UI 框架**: TailwindCSS v4 + Reka UI (Shadcn Vue 版本)
- **状态管理**: Pinia + VueUse
- **数据存储**: RxDB (本地) + Chrome Storage API
- **路由**: Vue Router 4.5
- **国际化**: @wxt-dev/i18n
- **图标**: Lucide Vue Next + Iconify

### 扩展架构设计

#### 1. 入口点架构 (Entrypoints)
```
entrypoints/
├── background/           # 后台服务
│   ├── index.ts         # 主入口
│   ├── ai-service.ts    # AI 模型服务管理
│   ├── context-manager.ts # 网页内容管理
│   ├── auth-manager.ts  # 认证管理
│   └── billing-manager.ts # 计费管理
├── content/             # 内容脚本
│   ├── index.ts         # 主入口
│   ├── page-analyzer.ts # 页面内容分析
│   ├── selection-handler.ts # 文本选择处理
│   └── media-extractor.ts # 媒体资源提取
├── side-panel/          # 侧边栏主界面
│   ├── App.vue          # 主应用
│   ├── components/      # 组件
│   └── pages/           # 页面
├── popup/               # 弹窗界面
└── options/             # 设置页面
```

#### 2. 数据层架构

##### 本地存储 (RxDB)
```typescript
// 数据库 Schema 设计
interface ChatSession {
  id: string;
  tabId: number;
  title: string;
  model: string;
  context: PageContext;
  messages: Message[];
  createdAt: string;
  updatedAt: string;
}

interface Message {
  id: string;
  sessionId: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
  tokens?: number;
}

interface PageContext {
  url: string;
  title: string;
  content: string;
  selectedText?: string;
  mediaResources?: MediaResource[];
}

interface UserPreferences {
  defaultModel: string;
  apiKeys: Record<string, string>;
  prompts: SavedPrompt[];
  theme: 'light' | 'dark';
}
```

##### Chrome Storage 同步
- 用户设置和 API 密钥
- 预置 Prompt 模板
- 主题和界面配置

#### 3. AI 服务架构

##### 多模型支持
```typescript
interface AIProvider {
  name: string;
  models: AIModel[];
  apiEndpoint: string;
  authenticate(apiKey: string): Promise<boolean>;
  chat(messages: Message[], options: ChatOptions): Promise<Response>;
}

// 支持的 AI 提供商
const providers = [
  'OpenAI',      // GPT-4, GPT-3.5
  'Anthropic',   // Claude
  'Google',      // Gemini
  'Cohere',      // Command
  'Mistral',     // Mistral 7B/8x7B
  'Ollama',      // 本地模型
  'Azure OpenAI',
  'AWS Bedrock'
];
```

##### 流式响应处理
- Server-Sent Events (SSE) 支持
- 实时消息流显示
- 错误处理和重试机制

#### 4. 上下文管理架构

##### 页面内容提取
```typescript
interface ContentExtractor {
  extractText(): string;
  extractStructure(): PageStructure;
  extractMedia(): MediaResource[];
  extractSelection(): string;
  watchChanges(): Observable<ContentChange>;
}
```

##### 标签页隔离
- 每个标签页独立的对话会话
- 基于 tabId 的上下文隔离
- 标签页切换时的会话恢复

#### 5. 权限和安全架构

##### 权限管理
```json
{
  "permissions": [
    "sidePanel",
    "tabs",
    "activeTab", 
    "storage",
    "contextMenus",
    "identity",
    "scripting"
  ],
  "host_permissions": [
    "https://*/*",
    "http://*/*"
  ]
}
```

##### 安全措施
- API 密钥加密存储
- 内容脚本沙箱隔离
- CSP 策略配置
- 敏感数据本地化处理

## 核心业务实现架构

### 1. 对话系统
```typescript
// 对话管理器
class ChatManager {
  private sessions: Map<number, ChatSession> = new Map();
  
  async createSession(tabId: number): Promise<ChatSession>;
  async sendMessage(sessionId: string, content: string): Promise<void>;
  async switchModel(sessionId: string, model: string): Promise<void>;
  async exportSession(sessionId: string): Promise<string>;
}
```

### 2. 上下文感知
```typescript
// 上下文提供器
class ContextProvider {
  async getPageContext(tabId: number): Promise<PageContext>;
  async getSelectedText(tabId: number): Promise<string>;
  async extractMediaInfo(element: HTMLElement): Promise<MediaResource>;
  watchPageChanges(tabId: number): Observable<PageChange>;
}
```

### 3. 模型管理
```typescript
// AI 模型管理器
class ModelManager {
  private providers: Map<string, AIProvider> = new Map();
  
  async registerProvider(provider: AIProvider): Promise<void>;
  async validateApiKey(provider: string, apiKey: string): Promise<boolean>;
  async chat(provider: string, model: string, messages: Message[]): Promise<Response>;
  getAvailableModels(): AIModel[];
}
```

### 4. 计费系统
```typescript
// 计费管理器
class BillingManager {
  async trackUsage(sessionId: string, tokens: number): Promise<void>;
  async checkQuota(userId: string): Promise<QuotaInfo>;
  async purchaseCredits(amount: number): Promise<PaymentResult>;
  getUsageStats(period: string): Promise<UsageStats>;
}
```

## 关键开源库选择

### UI 组件库
- **Reka UI**: Vue 版本的 Radix UI，提供无样式的可访问组件
- **Lucide Vue Next**: 现代图标库
- **Vue Sonner**: 优雅的通知组件

### 数据管理
- **RxDB**: 响应式本地数据库，支持离线和同步
- **Pinia**: Vue 3 官方状态管理
- **VueUse**: Vue 组合式 API 工具集

### 网络请求
- **Fetch API**: 原生 HTTP 客户端
- **EventSource**: SSE 流式响应处理

### 工具库
- **Zod**: TypeScript 优先的数据验证
- **date-fns**: 现代日期处理库
- **cuid**: 唯一 ID 生成器

### 开发工具
- **WXT**: 现代扩展开发框架
- **Vite**: 快速构建工具
- **TypeScript**: 类型安全
- **TailwindCSS**: 原子化 CSS 框架

## 详细任务清单

### 阶段一：基础架构搭建 (1-2 周)

#### 1.1 项目结构优化
- [ ] 重构现有目录结构
- [ ] 配置 TypeScript 严格模式
- [ ] 设置 ESLint + Prettier
- [ ] 配置 Vite 构建优化

#### 1.2 数据库设计
- [ ] 设计 RxDB Schema (ChatSession, Message, UserPreferences)
- [ ] 实现数据库迁移策略
- [ ] 创建数据访问层 (DAL)
- [ ] 实现数据同步机制

#### 1.3 基础组件开发
- [ ] 创建聊天界面组件
- [ ] 实现消息气泡组件
- [ ] 开发模型选择器
- [ ] 创建设置面板组件

### 阶段二：核心功能实现 (2-3 周)

#### 2.1 AI 服务集成
- [ ] 实现 AI Provider 抽象层
- [ ] 集成 OpenAI API
- [ ] 集成 Anthropic Claude
- [ ] 集成 Google Gemini
- [ ] 实现流式响应处理
- [ ] 添加错误处理和重试

#### 2.2 上下文管理
- [ ] 实现页面内容提取
- [ ] 开发文本选择监听
- [ ] 创建媒体资源提取器
- [ ] 实现标签页隔离机制
- [ ] 添加上下文压缩算法

#### 2.3 对话系统
- [ ] 实现对话会话管理
- [ ] 开发消息发送/接收
- [ ] 创建对话历史存储
- [ ] 实现会话导出功能
- [ ] 添加对话搜索功能

### 阶段三：高级功能开发 (2-3 周)

#### 3.1 Prompt 管理
- [ ] 设计 Prompt 模板系统
- [ ] 实现预置 Prompt 库
- [ ] 开发自定义 Prompt 编辑器
- [ ] 创建 Prompt 分享机制
- [ ] 实现 Prompt 变量替换

#### 3.2 用户体验优化
- [ ] 实现主题切换
- [ ] 添加快捷键支持
- [ ] 开发拖拽调整界面
- [ ] 实现消息编辑功能
- [ ] 添加打字机效果

#### 3.3 右键菜单集成
- [ ] 实现图片右键分析
- [ ] 添加视频信息提取
- [ ] 创建文本快速对话
- [ ] 开发链接内容分析

### 阶段四：认证和计费 (1-2 周)

#### 4.1 用户认证
- [ ] 集成 OAuth 2.0 认证
- [ ] 实现用户会话管理
- [ ] 开发账户同步功能
- [ ] 添加多设备支持

#### 4.2 计费系统
- [ ] 实现 Token 使用统计
- [ ] 开发配额管理
- [ ] 集成支付网关
- [ ] 创建使用报告

### 阶段五：测试和优化 (1 周)

#### 5.1 测试覆盖
- [ ] 单元测试 (Vitest)
- [ ] 集成测试
- [ ] E2E 测试 (Playwright)
- [ ] 性能测试

#### 5.2 性能优化
- [ ] 代码分割优化
- [ ] 内存使用优化
- [ ] 网络请求优化
- [ ] 缓存策略实现

#### 5.3 发布准备
- [ ] 构建流程优化
- [ ] 文档编写
- [ ] Chrome Web Store 准备
- [ ] 用户手册制作

## 开发规范

### 代码规范
- 使用 TypeScript 严格模式
- 遵循 Vue 3 Composition API 最佳实践
- 采用函数式编程风格
- 实现完整的错误处理

### 提交规范
- 使用 Conventional Commits
- 每个功能独立分支开发
- 代码审查后合并主分支

### 测试策略
- 单元测试覆盖率 > 80%
- 关键路径集成测试
- 用户体验 E2E 测试

## 风险评估

### 技术风险
- Chrome API 变更风险 - 中等
- AI 服务稳定性风险 - 中等
- 性能优化挑战 - 低

### 业务风险
- 用户隐私合规 - 高
- API 成本控制 - 中等
- 竞品压力 - 中等

## 后续扩展计划

### 功能扩展
- 支持更多 AI 模型
- 添加语音对话功能
- 实现协作对话功能
- 开发移动端支持

### 平台扩展
- Firefox 扩展版本
- Safari 扩展版本
- Edge 扩展版本
- 独立 Web 应用

---

*本文档将随着开发进度持续更新和完善*
