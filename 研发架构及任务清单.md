# GoChat Chrome 扩展研发架构及任务清单

## 项目概述

基于 Vue 3 + WXT + TailwindCSS v4 + Shadcn 的智能对话 Chrome 扩展，支持多模型切换、网页内容上下文感知、标签页级隔离的对话体验。

## 技术架构

### 核心技术栈
- **前端框架**: Vue 3.5 + Composition API + TypeScript
- **扩展框架**: WXT 0.20.7 (基于 Vite)
- **UI 框架**: TailwindCSS v4 + Reka UI (Shadcn Vue 版本)
- **状态管理**: Pinia + VueUse
- **数据存储**: RxDB (本地) + Chrome Storage API
- **路由**: Vue Router 4.5
- **国际化**: @wxt-dev/i18n
- **图标**: Lucide Vue Next + Iconify

### 扩展架构设计

#### 1. 入口点架构 (Entrypoints)
```
entrypoints/
├── background/           # 后台服务
│   ├── index.ts         # 主入口
│   ├── ai-service.ts    # AI 模型服务管理
│   ├── context-manager.ts # 网页内容管理
│   ├── auth-manager.ts  # 认证管理
│   └── billing-manager.ts # 计费管理
├── content/             # 内容脚本
│   ├── index.ts         # 主入口
│   ├── page-analyzer.ts # 页面内容分析
│   ├── selection-handler.ts # 文本选择处理
│   └── media-extractor.ts # 媒体资源提取
├── side-panel/          # 侧边栏主界面
│   ├── App.vue          # 主应用
│   ├── components/      # 组件
│   └── pages/           # 页面
├── popup/               # 弹窗界面
└── options/             # 设置页面
```

#### 2. 数据层架构

##### 本地存储 (RxDB)
```typescript
// 数据库 Schema 设计
interface ChatSession {
  id: string;
  tabId: number;
  title: string;
  model: string;
  context: PageContext;
  messages: Message[];
  userId?: string;          // 注册用户ID，匿名用户为空
  deviceId: string;         // 设备指纹ID
  userType: UserType;       // 用户类型
  createdAt: string;
  updatedAt: string;
}

interface Message {
  id: string;
  sessionId: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
  tokens?: number;
  cost?: number;            // 消耗的配额
}

interface PageContext {
  url: string;
  title: string;
  content: string;
  selectedText?: string;
  mediaResources?: MediaResource[];
}

interface UserPreferences {
  defaultModel: string;
  apiKeys: Record<string, string>;
  prompts: SavedPrompt[];
  theme: 'light' | 'dark';
  userType: UserType;
}

interface QuotaUsage {
  id: string;
  userId?: string;          // 注册用户ID
  deviceId: string;         // 匿名用户设备ID
  date: string;             // YYYY-MM-DD
  tokensUsed: number;
  tokensLimit: number;
  userType: UserType;
  resetAt: string;
  createdAt: string;
  updatedAt: string;
}
```

##### Chrome Storage 同步
- 用户设置和 API 密钥
- 预置 Prompt 模板
- 主题和界面配置

#### 3. AI 服务架构

##### 多模型支持
```typescript
interface AIProvider {
  name: string;
  models: AIModel[];
  apiEndpoint: string;
  authenticate(apiKey: string): Promise<boolean>;
  chat(messages: Message[], options: ChatOptions): Promise<Response>;
}

// 支持的 AI 提供商
const providers = [
  'OpenAI',      // GPT-4, GPT-3.5
  'Anthropic',   // Claude
  'Google',      // Gemini
  'Cohere',      // Command
  'Mistral',     // Mistral 7B/8x7B
  'Ollama',      // 本地模型
  'Azure OpenAI',
  'AWS Bedrock'
];
```

##### 流式响应处理
- Server-Sent Events (SSE) 支持
- 实时消息流显示
- 错误处理和重试机制

#### 4. 上下文管理架构

##### 页面内容提取
```typescript
interface ContentExtractor {
  extractText(): string;
  extractStructure(): PageStructure;
  extractMedia(): MediaResource[];
  extractSelection(): string;
  watchChanges(): Observable<ContentChange>;
}
```

##### 标签页隔离
- 每个标签页独立的对话会话
- 基于 tabId 的上下文隔离
- 标签页切换时的会话恢复

#### 5. 用户权限和配额架构

##### 用户类型定义
```typescript
enum UserType {
  ANONYMOUS = 'anonymous',    // 未登录用户
  REGISTERED = 'registered',  // 已注册用户
  PREMIUM = 'premium'         // 付费用户
}

interface UserQuota {
  userType: UserType;
  dailyTokenLimit: number;
  availableModels: string[];
  features: string[];
  resetTime: string;
}

// 配额配置
const QUOTA_CONFIG = {
  [UserType.ANONYMOUS]: {
    dailyTokenLimit: 10000,     // 每日1万token
    availableModels: ['gpt-3.5-turbo', 'claude-3-haiku'],
    features: ['basic_chat'],
    resetTime: '00:00:00'       // 每日重置
  },
  [UserType.REGISTERED]: {
    dailyTokenLimit: 50000,     // 每日5万token
    availableModels: ['gpt-3.5-turbo', 'gpt-4o-mini', 'claude-3-haiku', 'claude-3-sonnet'],
    features: ['basic_chat', 'context_analysis', 'prompt_save'],
    resetTime: '00:00:00'
  },
  [UserType.PREMIUM]: {
    dailyTokenLimit: 500000,    // 每日50万token
    availableModels: ['*'],     // 所有模型
    features: ['*'],            // 所有功能
    resetTime: '00:00:00'
  }
};
```

##### 匿名用户识别策略
```typescript
interface AnonymousUser {
  deviceId: string;           // 基于浏览器指纹生成
  installId: string;          // 扩展安装时生成的唯一ID
  sessionId: string;          // 当前会话ID
  createdAt: string;
  lastActiveAt: string;
}

// 设备指纹生成
class DeviceFingerprint {
  static async generate(): Promise<string> {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    ctx.textBaseline = 'top';
    ctx.font = '14px Arial';
    ctx.fillText('Device fingerprint', 2, 2);

    const fingerprint = {
      canvas: canvas.toDataURL(),
      userAgent: navigator.userAgent,
      language: navigator.language,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      screen: `${screen.width}x${screen.height}`,
      colorDepth: screen.colorDepth,
      platform: navigator.platform
    };

    return btoa(JSON.stringify(fingerprint)).slice(0, 32);
  }
}
```

##### 权限管理
```json
{
  "permissions": [
    "sidePanel",
    "tabs",
    "activeTab",
    "storage",
    "contextMenus",
    "identity",
    "scripting"
  ],
  "host_permissions": [
    "https://*/*",
    "http://*/*"
  ]
}
```

##### 安全措施
- 设备指纹 + 安装ID 双重匿名用户识别
- API 密钥服务端代理，客户端不存储
- 内容脚本沙箱隔离
- CSP 策略配置
- 配额数据加密存储

## 核心业务实现架构

### 1. 匿名用户支持架构

#### 1.1 匿名用户生命周期管理
```typescript
class AnonymousUserLifecycle {
  // 用户首次使用时初始化
  async initializeAnonymousUser(): Promise<AnonymousUser> {
    const deviceId = await DeviceFingerprint.generate();
    const installId = await this.getOrCreateInstallId();

    const anonymousUser: AnonymousUser = {
      deviceId,
      installId,
      sessionId: cuid(),
      createdAt: new Date().toISOString(),
      lastActiveAt: new Date().toISOString()
    };

    // 初始化配额
    await this.initializeQuota(deviceId);
    return anonymousUser;
  }

  // 配额检查和消费
  async consumeTokens(deviceId: string, tokens: number): Promise<boolean> {
    const quota = await this.getQuota(deviceId);
    if (quota.tokensUsed + tokens > quota.tokensLimit) {
      throw new QuotaExceededException('Daily quota exceeded');
    }

    quota.tokensUsed += tokens;
    await this.saveQuota(deviceId, quota);
    return true;
  }

  // 升级为注册用户
  async upgradeToRegistered(deviceId: string, userId: string): Promise<void> {
    // 迁移匿名用户数据到注册用户
    const sessions = await this.getAnonymousSessions(deviceId);
    const quota = await this.getQuota(deviceId);

    // 更新会话归属
    for (const session of sessions) {
      session.userId = userId;
      session.userType = UserType.REGISTERED;
      await this.updateSession(session);
    }

    // 迁移配额数据
    quota.userId = userId;
    quota.userType = UserType.REGISTERED;
    quota.tokensLimit = QUOTA_CONFIG[UserType.REGISTERED].dailyTokenLimit;
    await this.saveQuota(deviceId, quota);
  }
}
```

#### 1.2 配额限制实现
```typescript
class QuotaEnforcer {
  async enforceModelAccess(userType: UserType, requestedModel: string): Promise<boolean> {
    const config = QUOTA_CONFIG[userType];

    // 检查模型访问权限
    if (config.availableModels.includes('*') ||
        config.availableModels.includes(requestedModel)) {
      return true;
    }

    throw new ModelAccessDeniedException(
      `Model ${requestedModel} not available for ${userType} users`
    );
  }

  async enforceFeatureAccess(userType: UserType, feature: string): Promise<boolean> {
    const config = QUOTA_CONFIG[userType];

    if (config.features.includes('*') || config.features.includes(feature)) {
      return true;
    }

    throw new FeatureAccessDeniedException(
      `Feature ${feature} not available for ${userType} users`
    );
  }

  async enforceTokenLimit(deviceId: string, requestedTokens: number): Promise<void> {
    const usage = await this.getUsageToday(deviceId);
    const userType = await this.getUserType(deviceId);
    const limit = QUOTA_CONFIG[userType].dailyTokenLimit;

    if (usage.tokensUsed + requestedTokens > limit) {
      throw new QuotaExceededException({
        current: usage.tokensUsed,
        requested: requestedTokens,
        limit: limit,
        resetTime: usage.resetAt
      });
    }
  }
}
```

#### 1.3 服务端代理架构
```typescript
// 后端 API 代理服务
class AIProxyService {
  async proxyRequest(request: ChatRequest): Promise<ChatResponse> {
    // 验证用户身份和配额
    const user = await this.authenticateUser(request.deviceId, request.userId);
    await this.enforceQuota(user, request.estimatedTokens);

    // 代理请求到 AI 服务
    const response = await this.forwardToAIProvider(request);

    // 记录实际使用量
    await this.recordUsage(user, response.actualTokens);

    return response;
  }

  private async authenticateUser(deviceId: string, userId?: string): Promise<UserContext> {
    if (userId) {
      // 注册用户验证
      return await this.validateRegisteredUser(userId);
    } else {
      // 匿名用户验证
      return await this.validateAnonymousUser(deviceId);
    }
  }
}
```

### 2. 前端用户体验设计

#### 2.1 匿名用户引导流程
```typescript
// 用户引导组件
interface UserOnboardingFlow {
  // 首次使用引导
  showWelcomeDialog(): void;

  // 配额状态显示
  displayQuotaStatus(usage: QuotaUsage): void;

  // 升级提醒
  showUpgradePrompt(reason: 'quota_exceeded' | 'model_restricted'): void;

  // 功能限制提示
  showFeatureLimitedTooltip(feature: string): void;
}

// 配额状态组件
const QuotaIndicator = {
  template: `
    <div class="quota-indicator" :class="quotaStatusClass">
      <Icon :name="quotaIcon" />
      <span>{{ quotaText }}</span>
      <Button v-if="showUpgrade" @click="showUpgradeDialog">升级</Button>
    </div>
  `,
  computed: {
    quotaStatusClass() {
      if (this.usage.percentage > 90) return 'quota-critical';
      if (this.usage.percentage > 70) return 'quota-warning';
      return 'quota-normal';
    }
  }
};
```

#### 2.2 模型选择器增强
```typescript
// 模型选择组件
const ModelSelector = {
  template: `
    <Select v-model="selectedModel">
      <SelectTrigger>
        <SelectValue placeholder="选择模型" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem
          v-for="model in availableModels"
          :key="model.id"
          :value="model.id"
          :disabled="!model.accessible"
        >
          <div class="flex items-center justify-between w-full">
            <span>{{ model.name }}</span>
            <Badge v-if="model.isPremium" variant="premium">Pro</Badge>
            <Tooltip v-if="!model.accessible">
              <TooltipTrigger>
                <Icon name="lock" class="w-4 h-4" />
              </TooltipTrigger>
              <TooltipContent>
                需要注册账户才能使用此模型
              </TooltipContent>
            </Tooltip>
          </div>
        </SelectItem>
      </SelectContent>
    </Select>
  `
};
```

### 3. 对话系统
```typescript
// 对话管理器
class ChatManager {
  private sessions: Map<number, ChatSession> = new Map();

  async createSession(tabId: number, userType: UserType): Promise<ChatSession>;
  async sendMessage(sessionId: string, content: string): Promise<void>;
  async switchModel(sessionId: string, model: string): Promise<void>;
  async exportSession(sessionId: string): Promise<string>;
  async checkQuotaBeforeSend(sessionId: string, estimatedTokens: number): Promise<boolean>;
}
```

### 2. 上下文感知
```typescript
// 上下文提供器
class ContextProvider {
  async getPageContext(tabId: number): Promise<PageContext>;
  async getSelectedText(tabId: number): Promise<string>;
  async extractMediaInfo(element: HTMLElement): Promise<MediaResource>;
  watchPageChanges(tabId: number): Observable<PageChange>;
}
```

### 3. 模型管理
```typescript
// AI 模型管理器
class ModelManager {
  private providers: Map<string, AIProvider> = new Map();
  
  async registerProvider(provider: AIProvider): Promise<void>;
  async validateApiKey(provider: string, apiKey: string): Promise<boolean>;
  async chat(provider: string, model: string, messages: Message[]): Promise<Response>;
  getAvailableModels(): AIModel[];
}
```

### 4. 配额和计费系统
```typescript
// 配额管理器
class QuotaManager {
  private quotaStorage: Map<string, UserQuota> = new Map();

  async getUserQuota(userId: string): Promise<UserQuota>;
  async checkQuotaAvailable(userId: string, tokens: number): Promise<boolean>;
  async consumeQuota(userId: string, tokens: number): Promise<void>;
  async resetDailyQuota(userId: string): Promise<void>;
  async upgradeUserType(userId: string, newType: UserType): Promise<void>;
}

// 匿名用户管理器
class AnonymousUserManager {
  async getOrCreateAnonymousUser(): Promise<AnonymousUser>;
  async trackAnonymousUsage(deviceId: string, tokens: number): Promise<void>;
  async checkAnonymousQuota(deviceId: string): Promise<QuotaInfo>;
  async promoteToRegistered(deviceId: string, userId: string): Promise<void>;
}

// 计费管理器
class BillingManager {
  async trackUsage(sessionId: string, tokens: number, userType: UserType): Promise<void>;
  async checkQuota(userId: string): Promise<QuotaInfo>;
  async purchaseCredits(amount: number): Promise<PaymentResult>;
  getUsageStats(period: string): Promise<UsageStats>;
  async handleQuotaExceeded(userId: string): Promise<UpgradeOptions>;
}
```

## 关键开源库选择

### UI 组件库
- **Reka UI**: Vue 版本的 Radix UI，提供无样式的可访问组件
- **Lucide Vue Next**: 现代图标库
- **Vue Sonner**: 优雅的通知组件

### 数据管理
- **RxDB**: 响应式本地数据库，支持离线和同步
- **Pinia**: Vue 3 官方状态管理
- **VueUse**: Vue 组合式 API 工具集

### 网络请求
- **Fetch API**: 原生 HTTP 客户端
- **EventSource**: SSE 流式响应处理

### 工具库
- **Zod**: TypeScript 优先的数据验证
- **date-fns**: 现代日期处理库
- **cuid**: 唯一 ID 生成器

### 开发工具
- **WXT**: 现代扩展开发框架
- **Vite**: 快速构建工具
- **TypeScript**: 类型安全
- **TailwindCSS**: 原子化 CSS 框架

## 详细任务清单

### 阶段一：基础架构搭建 (1-2 周)

#### 1.1 项目结构优化
- [ ] 重构现有目录结构
- [ ] 配置 TypeScript 严格模式
- [ ] 设置 ESLint + Prettier
- [ ] 配置 Vite 构建优化

#### 1.2 用户系统设计
- [ ] 实现匿名用户识别机制
- [ ] 设计设备指纹生成算法
- [ ] 创建用户类型管理系统
- [ ] 实现配额管理架构

#### 1.3 数据库设计
- [ ] 设计 RxDB Schema (ChatSession, Message, UserPreferences, QuotaUsage)
- [ ] 实现数据库迁移策略
- [ ] 创建数据访问层 (DAL)
- [ ] 实现数据同步机制

#### 1.4 基础组件开发
- [ ] 创建聊天界面组件
- [ ] 实现消息气泡组件
- [ ] 开发增强型模型选择器（支持权限显示）
- [ ] 创建配额状态指示器
- [ ] 实现用户引导组件
- [ ] 开发升级提醒对话框
- [ ] 创建设置面板组件

### 阶段二：核心功能实现 (2-3 周)

#### 2.1 AI 服务集成
- [ ] 实现 AI Provider 抽象层
- [ ] 集成 OpenAI API
- [ ] 集成 Anthropic Claude
- [ ] 集成 Google Gemini
- [ ] 实现流式响应处理
- [ ] 添加错误处理和重试

#### 2.2 上下文管理
- [ ] 实现页面内容提取
- [ ] 开发文本选择监听
- [ ] 创建媒体资源提取器
- [ ] 实现标签页隔离机制
- [ ] 添加上下文压缩算法

#### 2.3 对话系统
- [ ] 实现对话会话管理（支持匿名用户）
- [ ] 开发消息发送/接收（集成配额检查）
- [ ] 创建对话历史存储
- [ ] 实现会话导出功能
- [ ] 添加对话搜索功能
- [ ] 实现匿名用户数据迁移机制

### 阶段三：高级功能开发 (2-3 周)

#### 3.1 Prompt 管理
- [ ] 设计 Prompt 模板系统
- [ ] 实现预置 Prompt 库
- [ ] 开发自定义 Prompt 编辑器
- [ ] 创建 Prompt 分享机制
- [ ] 实现 Prompt 变量替换

#### 3.2 用户体验优化
- [ ] 实现主题切换
- [ ] 添加快捷键支持
- [ ] 开发拖拽调整界面
- [ ] 实现消息编辑功能
- [ ] 添加打字机效果

#### 3.3 右键菜单集成
- [ ] 实现图片右键分析
- [ ] 添加视频信息提取
- [ ] 创建文本快速对话
- [ ] 开发链接内容分析

### 阶段四：认证和计费 (1-2 周)

#### 4.1 用户认证系统
- [ ] 集成 OAuth 2.0 认证
- [ ] 实现用户会话管理
- [ ] 开发匿名用户升级机制
- [ ] 实现账户数据迁移
- [ ] 添加多设备支持

#### 4.2 配额和计费系统
- [ ] 实现匿名用户配额管理
- [ ] 开发 Token 使用统计
- [ ] 创建配额超限提醒
- [ ] 实现配额重置机制
- [ ] 集成支付网关
- [ ] 创建使用报告和分析

### 阶段五：测试和优化 (1 周)

#### 5.1 测试覆盖
- [ ] 单元测试 (Vitest)
- [ ] 集成测试
- [ ] E2E 测试 (Playwright)
- [ ] 性能测试

#### 5.2 性能优化
- [ ] 代码分割优化
- [ ] 内存使用优化
- [ ] 网络请求优化
- [ ] 缓存策略实现

#### 5.3 发布准备
- [ ] 构建流程优化
- [ ] 文档编写
- [ ] Chrome Web Store 准备
- [ ] 用户手册制作

## 开发规范

### 代码规范
- 使用 TypeScript 严格模式
- 遵循 Vue 3 Composition API 最佳实践
- 采用函数式编程风格
- 实现完整的错误处理

### 提交规范
- 使用 Conventional Commits
- 每个功能独立分支开发
- 代码审查后合并主分支

### 测试策略
- 单元测试覆盖率 > 80%
- 关键路径集成测试
- 用户体验 E2E 测试

## 匿名用户支持的技术实现要点

### 1. 设备指纹稳定性
- **挑战**: 浏览器更新、隐私模式可能导致指纹变化
- **解决方案**: 多重标识符组合 + 模糊匹配算法
- **备选方案**: Chrome Extension Storage API 存储持久化ID

### 2. 配额防刷机制
- **IP限制**: 同一IP每日最大设备数限制
- **行为分析**: 异常使用模式检测
- **时间窗口**: 滑动窗口配额控制
- **设备验证**: 浏览器环境一致性检查

### 3. 数据迁移策略
```typescript
// 匿名用户升级时的数据迁移
class DataMigrationService {
  async migrateAnonymousData(deviceId: string, userId: string): Promise<void> {
    // 1. 迁移对话历史
    const sessions = await this.getAnonymousSessions(deviceId);
    await this.transferSessionsToUser(sessions, userId);

    // 2. 迁移配额使用记录
    const quotaHistory = await this.getQuotaHistory(deviceId);
    await this.mergeQuotaHistory(quotaHistory, userId);

    // 3. 迁移用户偏好设置
    const preferences = await this.getAnonymousPreferences(deviceId);
    await this.mergeUserPreferences(preferences, userId);

    // 4. 清理匿名数据
    await this.cleanupAnonymousData(deviceId);
  }
}
```

### 4. 前端状态管理
```typescript
// Pinia Store for User State
export const useUserStore = defineStore('user', () => {
  const userType = ref<UserType>(UserType.ANONYMOUS);
  const deviceId = ref<string>('');
  const userId = ref<string | null>(null);
  const quotaUsage = ref<QuotaUsage | null>(null);

  const isAnonymous = computed(() => userType.value === UserType.ANONYMOUS);
  const canUseModel = (modelId: string) => {
    const config = QUOTA_CONFIG[userType.value];
    return config.availableModels.includes('*') ||
           config.availableModels.includes(modelId);
  };

  const remainingQuota = computed(() => {
    if (!quotaUsage.value) return 0;
    return quotaUsage.value.tokensLimit - quotaUsage.value.tokensUsed;
  });

  return {
    userType,
    deviceId,
    userId,
    quotaUsage,
    isAnonymous,
    canUseModel,
    remainingQuota
  };
});
```

## 风险评估

### 技术风险
- Chrome API 变更风险 - 中等
- AI 服务稳定性风险 - 中等
- 设备指纹准确性风险 - 中等
- 配额防刷绕过风险 - 中等
- 性能优化挑战 - 低

### 业务风险
- 用户隐私合规 - 高
- API 成本控制 - 高（匿名用户增加成本控制难度）
- 免费额度滥用 - 中等
- 竞品压力 - 中等

## 后续扩展计划

### 功能扩展
- 支持更多 AI 模型
- 添加语音对话功能
- 实现协作对话功能
- 开发移动端支持

### 平台扩展
- Firefox 扩展版本
- Safari 扩展版本
- Edge 扩展版本
- 独立 Web 应用

---

*本文档将随着开发进度持续更新和完善*
